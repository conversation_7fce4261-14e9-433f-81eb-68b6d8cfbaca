# Static Inventory File
# This file defines the infrastructure hosts and groups

[webservers]
web01 ansible_host=************ ansible_user=ubuntu
web02 ansible_host=************ ansible_user=ubuntu
web03 ansible_host=************ ansible_user=ubuntu

[databases]
db01 ansible_host=************ ansible_user=ubuntu mysql_role=master
db02 ansible_host=************ ansible_user=ubuntu mysql_role=slave

[loadbalancers]
lb01 ansible_host=************ ansible_user=ubuntu

[monitoring]
monitor01 ansible_host=************ ansible_user=ubuntu

[vps_servers]
vps7 ansible_host=vps7.example.com ansible_user=deploy
vps7-staging ansible_host=vps7-staging.example.com ansible_user=deploy

# Group of groups
[production:children]
webservers
databases
loadbalancers
monitoring

[staging:children]
vps_servers

[all:vars]
ansible_ssh_common_args='-o StrictHostKeyChecking=no'
ansible_python_interpreter=/usr/bin/python3