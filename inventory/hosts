# Enterprise DevOps Stack Inventory
# Complete infrastructure definition for enterprise deployment

# ============================================================================
# CORE INFRASTRUCTURE
# ============================================================================

[webservers]
web01 ansible_host=************ ansible_user=ubuntu
web02 ansible_host=************ ansible_user=ubuntu
web03 ansible_host=************ ansible_user=ubuntu

[databases]
db01 ansible_host=************ ansible_user=ubuntu mysql_role=master
db02 ansible_host=************ ansible_user=ubuntu mysql_role=slave

[loadbalancers]
lb01 ansible_host=************ ansible_user=ubuntu

# ============================================================================
# KUBERNETES CLUSTER
# ============================================================================

[kubernetes_masters]
k8s-master01 ansible_host=************ ansible_user=ubuntu
k8s-master02 ansible_host=************ ansible_user=ubuntu
k8s-master03 ansible_host=************ ansible_user=ubuntu

[kubernetes_workers]
k8s-worker01 ansible_host=************ ansible_user=ubuntu
k8s-worker02 ansible_host=************ ansible_user=ubuntu
k8s-worker03 ansible_host=************ ansible_user=ubuntu
k8s-worker04 ansible_host=************ ansible_user=ubuntu

# ============================================================================
# CI/CD INFRASTRUCTURE
# ============================================================================

[jenkins_servers]
jenkins01 ansible_host=************ ansible_user=ubuntu

[cicd_servers:children]
jenkins_servers

# ============================================================================
# MONITORING & OBSERVABILITY
# ============================================================================

[monitoring_servers]
monitor01 ansible_host=************ ansible_user=ubuntu
monitor02 ansible_host=************ ansible_user=ubuntu

[logging_servers]
log01 ansible_host=************ ansible_user=ubuntu

# ============================================================================
# DEVOPS TOOLS
# ============================================================================

[devops_tools]
devops01 ansible_host=************ ansible_user=ubuntu

# ============================================================================
# SECURITY & VAULT
# ============================================================================

[vault_servers]
vault01 ansible_host=************0 ansible_user=ubuntu
vault02 ansible_host=************1 ansible_user=ubuntu
vault03 ansible_host=************2 ansible_user=ubuntu

[security_controllers]
security01 ansible_host=************0 ansible_user=ubuntu

# ============================================================================
# BACKUP & DISASTER RECOVERY
# ============================================================================

[backup_servers]
backup01 ansible_host=************0 ansible_user=ubuntu

[dr_test_servers]
dr-test01 ansible_host=************ ansible_user=ubuntu

# ============================================================================
# MULTI-CLOUD INFRASTRUCTURE
# ============================================================================

[cloud_management]
cloud-mgmt01 ansible_host=************* ansible_user=ubuntu

[terraform_runners]
terraform01 ansible_host=************* ansible_user=ubuntu

# AWS Infrastructure
[aws_targets]
aws-web01 ansible_host=********* ansible_user=ec2-user
aws-web02 ansible_host=********* ansible_user=ec2-user

# Azure Infrastructure
[azure_targets]
azure-web01 ansible_host=********* ansible_user=azureuser
azure-web02 ansible_host=********* ansible_user=azureuser

# GCP Infrastructure
[gcp_targets]
gcp-web01 ansible_host=********* ansible_user=gcp-user
gcp-web02 ansible_host=********* ansible_user=gcp-user

# ============================================================================
# NETWORK & TESTING
# ============================================================================

[network_controllers]
network01 ansible_host=************* ansible_user=ubuntu

[test_controllers]
test01 ansible_host=************* ansible_user=ubuntu

# ============================================================================
# LEGACY SYSTEMS
# ============================================================================

[vps_servers]
vps7 ansible_host=vps7.example.com ansible_user=deploy
vps7-staging ansible_host=vps7-staging.example.com ansible_user=deploy

# ============================================================================
# ENVIRONMENT GROUPS
# ============================================================================

[production:children]
webservers
databases
loadbalancers
kubernetes_masters
kubernetes_workers
monitoring_servers
vault_servers

[staging:children]
vps_servers

[development:children]
test_controllers

# ============================================================================
# FUNCTIONAL GROUPS
# ============================================================================

[kubernetes:children]
kubernetes_masters
kubernetes_workers

[monitoring:children]
monitoring_servers
logging_servers

[security:children]
vault_servers
security_controllers

[cloud_infrastructure:children]
aws_targets
azure_targets
gcp_targets

[terraform_managed:children]
aws_targets
azure_targets
gcp_targets

# ============================================================================
# GLOBAL VARIABLES
# ============================================================================

[all:vars]
ansible_ssh_common_args='-o StrictHostKeyChecking=no'
ansible_python_interpreter=/usr/bin/python3
ansible_ssh_pipelining=true