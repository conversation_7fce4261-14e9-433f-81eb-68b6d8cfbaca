#!/usr/bin/env python3
"""
Dynamic Inventory Script for Ansible
This script can be extended to pull inventory from cloud providers, CMDB, etc.
"""

import json
import argparse
import sys
import os

class DynamicInventory:
    def __init__(self):
        self.inventory = {}
        self.read_cli_args()
        
        # Called with `--list`.
        if self.args.list:
            self.inventory = self.get_inventory()
        # Called with `--host [hostname]`.
        elif self.args.host:
            self.inventory = self.get_host_info(self.args.host)
        # If no groups or vars are present, return an empty inventory.
        else:
            self.inventory = self.empty_inventory()

        print(json.dumps(self.inventory, indent=2))

    def get_inventory(self):
        """Return the inventory dictionary."""
        return {
            'webservers': {
                'hosts': ['web01', 'web02', 'web03'],
                'vars': {
                    'http_port': 80,
                    'maxRequestsPerChild': 808
                }
            },
            'databases': {
                'hosts': ['db01', 'db02'],
                'vars': {
                    'mysql_port': 3306,
                    'mysql_root_password': '{{ vault_mysql_root_password }}'
                }
            },
            'loadbalancers': {
                'hosts': ['lb01'],
                'vars': {
                    'nginx_port': 80
                }
            },
            'monitoring': {
                'hosts': ['monitor01'],
                'vars': {
                    'grafana_port': 3000,
                    'prometheus_port': 9090
                }
            },
            'production': {
                'children': ['webservers', 'databases', 'loadbalancers', 'monitoring']
            },
            '_meta': {
                'hostvars': {
                    'web01': {
                        'ansible_host': '************',
                        'ansible_user': 'ubuntu'
                    },
                    'web02': {
                        'ansible_host': '************',
                        'ansible_user': 'ubuntu'
                    },
                    'web03': {
                        'ansible_host': '************',
                        'ansible_user': 'ubuntu'
                    },
                    'db01': {
                        'ansible_host': '************',
                        'ansible_user': 'ubuntu',
                        'mysql_role': 'master'
                    },
                    'db02': {
                        'ansible_host': '************',
                        'ansible_user': 'ubuntu',
                        'mysql_role': 'slave'
                    },
                    'lb01': {
                        'ansible_host': '************',
                        'ansible_user': 'ubuntu'
                    },
                    'monitor01': {
                        'ansible_host': '************',
                        'ansible_user': 'ubuntu'
                    }
                }
            }
        }

    def get_host_info(self, host):
        """Return variables for a specific host."""
        inventory = self.get_inventory()
        if host in inventory['_meta']['hostvars']:
            return inventory['_meta']['hostvars'][host]
        else:
            return {}

    def empty_inventory(self):
        """Return an empty inventory."""
        return {'_meta': {'hostvars': {}}}

    def read_cli_args(self):
        """Read command line arguments."""
        parser = argparse.ArgumentParser()
        parser.add_argument('--list', action='store_true')
        parser.add_argument('--host', action='store')
        self.args = parser.parse_args()

if __name__ == '__main__':
    DynamicInventory()
