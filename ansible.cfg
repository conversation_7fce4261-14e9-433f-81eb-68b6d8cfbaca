[defaults]
# Basic Configuration
inventory = inventory/
remote_user = ansible
private_key_file = ~/.ssh/ansible_key
host_key_checking = False
timeout = 30
forks = 10
poll_interval = 15
transport = smart

# Logging
log_path = logs/ansible.log
display_skipped_hosts = False
display_ok_hosts = True
stdout_callback = yaml
bin_ansible_callbacks = True

# Performance
gathering = smart
fact_caching = jsonfile
fact_caching_connection = /tmp/ansible_facts_cache
fact_caching_timeout = 86400
pipelining = True
control_path_dir = /tmp/.ansible-cp

# Security
vault_password_file = .vault_pass
ask_vault_pass = False
vault_identity_list = default@.vault_pass

# Roles
roles_path = roles:~/.ansible/roles:/usr/share/ansible/roles:/etc/ansible/roles
collections_paths = collections:~/.ansible/collections:/usr/share/ansible/collections

# Plugins
callback_plugins = callback_plugins
filter_plugins = filter_plugins
library = library

# Retry files
retry_files_enabled = True
retry_files_save_path = logs/

# Deprecation warnings
deprecation_warnings = True
command_warnings = True

[inventory]
enable_plugins = host_list, script, auto, yaml, ini, toml

[privilege_escalation]
become = True
become_method = sudo
become_user = root
become_ask_pass = False

[paramiko_connection]
record_host_keys = False

[ssh_connection]
ssh_args = -C -o ControlMaster=auto -o ControlPersist=60s -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no
pipelining = True
control_path = /tmp/ansible-ssh-%%h-%%p-%%r
scp_if_ssh = smart
transfer_method = smart

[persistent_connection]
connect_timeout = 30
command_timeout = 30

[colors]
highlight = white
verbose = blue
warn = bright purple
error = red
debug = dark gray
deprecate = purple
skip = cyan
unreachable = red
ok = green
changed = yellow
diff_add = green
diff_remove = red
diff_lines = cyan
