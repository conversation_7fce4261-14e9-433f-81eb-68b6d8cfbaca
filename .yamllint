---
# YAML Lint configuration

extends: default

rules:
  line-length:
    max: 120
    level: warning
  
  indentation:
    spaces: 2
    indent-sequences: true
    check-multi-line-strings: false
  
  truthy:
    allowed-values: ['true', 'false', 'yes', 'no']
    check-keys: true
  
  comments:
    min-spaces-from-content: 1
  
  comments-indentation: disable
  
  document-start:
    present: true
  
  empty-lines:
    max: 2
    max-start: 0
    max-end: 1
