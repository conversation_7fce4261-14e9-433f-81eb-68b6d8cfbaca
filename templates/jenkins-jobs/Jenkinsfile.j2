pipeline {
    agent any
    
    environment {
        PROJECT_NAME = '{{ project_name }}'
        DOCKER_REGISTRY = '{{ docker_registry }}'
        DEPLOYMENT_ENV = '{{ deployment_environment }}'
        GIT_REPO = '{{ git_repo_url }}'
    }
    
    options {
        buildDiscarder(logRotator(numToKeepStr: '10'))
        timeout(time: 30, unit: 'MINUTES')
        timestamps()
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
                script {
                    env.GIT_COMMIT_SHORT = sh(
                        script: 'git rev-parse --short HEAD',
                        returnStdout: true
                    ).trim()
                    env.BUILD_VERSION = "${env.BUILD_NUMBER}-${env.GIT_COMMIT_SHORT}"
                }
            }
        }
        
        stage('Build') {
            steps {
                script {
                    echo "Building ${PROJECT_NAME} version ${BUILD_VERSION}"
                    sh '''
                        # Install dependencies
                        if [ -f "package.json" ]; then
                            npm install
                        elif [ -f "requirements.txt" ]; then
                            pip install -r requirements.txt
                        elif [ -f "pom.xml" ]; then
                            mvn clean compile
                        fi
                    '''
                }
            }
        }
        
        stage('Test') {
            parallel {
                stage('Unit Tests') {
                    steps {
                        script {
                            sh '''
                                # Run unit tests
                                if [ -f "package.json" ]; then
                                    npm test
                                elif [ -f "requirements.txt" ]; then
                                    python -m pytest tests/
                                elif [ -f "pom.xml" ]; then
                                    mvn test
                                fi
                            '''
                        }
                    }
                    post {
                        always {
                            publishTestResults testResultsPattern: '**/test-results.xml'
                        }
                    }
                }
                
                stage('Code Quality') {
                    steps {
                        script {
                            // SonarQube analysis
                            withSonarQubeEnv('SonarQube') {
                                sh '''
                                    sonar-scanner \
                                        -Dsonar.projectKey=${PROJECT_NAME} \
                                        -Dsonar.projectName=${PROJECT_NAME} \
                                        -Dsonar.projectVersion=${BUILD_VERSION}
                                '''
                            }
                        }
                    }
                }
            }
        }
        
        stage('Docker Build') {
            steps {
                script {
                    def dockerImage = docker.build("${DOCKER_REGISTRY}/${PROJECT_NAME}:${BUILD_VERSION}")
                    docker.withRegistry("https://${DOCKER_REGISTRY}", 'docker-registry-credentials') {
                        dockerImage.push()
                        dockerImage.push('latest')
                    }
                }
            }
        }
        
        stage('Security Scan') {
            steps {
                script {
                    // Container security scanning
                    sh """
                        docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
                            aquasec/trivy image ${DOCKER_REGISTRY}/${PROJECT_NAME}:${BUILD_VERSION}
                    """
                }
            }
        }
        
        stage('Deploy to Staging') {
            when {
                branch 'develop'
            }
            steps {
                script {
                    build job: "${PROJECT_NAME}-deploy", parameters: [
                        string(name: 'IMAGE_TAG', value: "${BUILD_VERSION}"),
                        string(name: 'ENVIRONMENT', value: 'staging')
                    ]
                }
            }
        }
        
        stage('Deploy to Production') {
            when {
                branch 'main'
            }
            steps {
                script {
                    input message: 'Deploy to Production?', ok: 'Deploy'
                    build job: "${PROJECT_NAME}-deploy", parameters: [
                        string(name: 'IMAGE_TAG', value: "${BUILD_VERSION}"),
                        string(name: 'ENVIRONMENT', value: 'production')
                    ]
                }
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        success {
            emailext (
                subject: "✅ Build Success: ${PROJECT_NAME} - ${BUILD_VERSION}",
                body: "Build ${BUILD_NUMBER} of ${PROJECT_NAME} completed successfully.",
                to: "${env.CHANGE_AUTHOR_EMAIL ?: '<EMAIL>'}"
            )
        }
        failure {
            emailext (
                subject: "❌ Build Failed: ${PROJECT_NAME} - ${BUILD_VERSION}",
                body: "Build ${BUILD_NUMBER} of ${PROJECT_NAME} failed. Check console output for details.",
                to: "${env.CHANGE_AUTHOR_EMAIL ?: '<EMAIL>'}"
            )
        }
    }
}
