version: '3.8'

services:
  sonarqube:
    image: sonarqube:{{ sonarqube_version }}
    container_name: sonarqube
    ports:
      - "9000:9000"
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
      - ./sonarqube/sonar.properties:/opt/sonarqube/conf/sonar.properties
    environment:
      - SONAR_JDBC_URL=*************************************
      - SONAR_JDBC_USERNAME=sonar
      - SONAR_JDBC_PASSWORD={{ vault_sonar_db_password | default('sonar') }}
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - devops-tools

  nexus:
    image: sonatype/nexus3:{{ nexus_version }}
    container_name: nexus
    ports:
      - "8081:8081"
    volumes:
      - nexus_data:/nexus-data
      - ./nexus/nexus.properties:/nexus-data/etc/nexus.properties
    environment:
      - INSTALL4J_ADD_VM_PARAMS=-Xms1g -Xmx1g -XX:MaxDirectMemorySize=2g
    restart: unless-stopped
    networks:
      - devops-tools

  gitlab:
    image: gitlab/gitlab-ce:{{ gitlab_version }}
    container_name: gitlab
    hostname: 'gitlab.{{ domain_name | default("example.com") }}'
    ports:
      - "80:80"
      - "443:443"
      - "22:22"
    volumes:
      - gitlab_config:/etc/gitlab
      - gitlab_logs:/var/log/gitlab
      - gitlab_data:/var/opt/gitlab
      - ./gitlab/gitlab.rb:/etc/gitlab/gitlab.rb
    environment:
      GITLAB_OMNIBUS_CONFIG: |
        external_url 'http://gitlab.{{ domain_name | default("example.com") }}'
        gitlab_rails['gitlab_shell_ssh_port'] = 22
    restart: unless-stopped
    networks:
      - devops-tools

  docker-registry:
    image: registry:2
    container_name: docker-registry
    ports:
      - "5000:5000"
    volumes:
      - registry_data:/var/lib/registry
      - ./registry/config.yml:/etc/docker/registry/config.yml
    environment:
      - REGISTRY_STORAGE_FILESYSTEM_ROOTDIRECTORY=/var/lib/registry
    restart: unless-stopped
    networks:
      - devops-tools

  postgres:
    image: postgres:13
    container_name: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=sonar
      - POSTGRES_USER=sonar
      - POSTGRES_PASSWORD={{ vault_sonar_db_password | default('sonar') }}
    restart: unless-stopped
    networks:
      - devops-tools

  redis:
    image: redis:7-alpine
    container_name: redis
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - devops-tools

  portainer:
    image: portainer/portainer-ce:latest
    container_name: portainer
    ports:
      - "9443:9443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    restart: unless-stopped
    networks:
      - devops-tools

volumes:
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
  nexus_data:
  gitlab_config:
  gitlab_logs:
  gitlab_data:
  registry_data:
  postgres_data:
  redis_data:
  portainer_data:

networks:
  devops-tools:
    driver: bridge
