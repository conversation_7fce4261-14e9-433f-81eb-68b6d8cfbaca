---
# <PERSON> role main tasks

- name: Include OS-specific variables
  include_vars: "{{ ansible_os_family }}.yml"
  tags: always

- name: Install Java
  include_tasks: java.yml
  tags: java

- name: Install Jenkins
  include_tasks: install.yml
  tags: install

- name: Configure <PERSON>
  include_tasks: configure.yml
  tags: configure

- name: Install Jenkins plugins
  include_tasks: plugins.yml
  tags: plugins

- name: Configure Jenkins as Code (JCasC)
  include_tasks: casc.yml
  when: jenkins_casc_enabled
  tags: casc

- name: Configure <PERSON> tools
  include_tasks: tools.yml
  tags: tools

- name: Create Jenkins jobs
  include_tasks: jobs.yml
  tags: jobs

- name: Configure backup
  include_tasks: backup.yml
  when: jenkins_backup_enabled
  tags: backup

- name: Configure SSL
  include_tasks: ssl.yml
  when: jenkins_ssl_enabled
  tags: ssl

- name: Start and enable Jenkins service
  service:
    name: jenkins
    state: started
    enabled: yes
  tags: service

- name: Wait for <PERSON> to start
  uri:
    url: "http://{{ jenkins_host }}:{{ jenkins_port }}{{ jenkins_url_prefix }}/login"
    method: GET
    status_code: 200
  retries: 60
  delay: 5
  tags: service
