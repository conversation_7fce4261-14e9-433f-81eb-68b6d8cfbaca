---
# <PERSON> jobs configuration

- name: Create sample pipeline job
  template:
    src: jobs/sample-pipeline/config.xml.j2
    dest: "{{ jenkins_home }}/jobs/sample-pipeline/config.xml"
    owner: "{{ jenkins_user }}"
    group: "{{ jenkins_group }}"
    mode: '0644'
  notify: restart jenkins
  tags: sample_job

- name: Create Docker build job
  template:
    src: jobs/docker-build/config.xml.j2
    dest: "{{ jenkins_home }}/jobs/docker-build/config.xml"
    owner: "{{ jenkins_user }}"
    group: "{{ jenkins_group }}"
    mode: '0644'
  notify: restart jenkins
  tags: docker_job

- name: Create deployment job
  template:
    src: jobs/deploy-app/config.xml.j2
    dest: "{{ jenkins_home }}/jobs/deploy-app/config.xml"
    owner: "{{ jenkins_user }}"
    group: "{{ jenkins_group }}"
    mode: '0644'
  notify: restart jenkins
  tags: deploy_job

- name: Create job directories
  file:
    path: "{{ jenkins_home }}/jobs/{{ item }}"
    state: directory
    owner: "{{ jenkins_user }}"
    group: "{{ jenkins_group }}"
    mode: '0755'
  with_items:
    - sample-pipeline
    - docker-build
    - deploy-app
  tags: job_directories
