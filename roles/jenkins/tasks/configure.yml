---
# <PERSON> configuration tasks

- name: Configure <PERSON> defaults
  template:
    src: jenkins.j2
    dest: /etc/default/jenkins
    backup: yes
  notify: restart jenkins
  tags: config

- name: Configure <PERSON> systemd service
  template:
    src: jenkins.service.j2
    dest: /etc/systemd/system/jenkins.service
    backup: yes
  notify:
    - reload systemd
    - restart jenkins
  when: ansible_service_mgr == "systemd"
  tags: config

- name: <PERSON><PERSON> <PERSON> setup wizard
  lineinfile:
    path: "{{ jenkins_home }}/config.xml"
    regexp: '<installStateName>'
    line: '  <installStateName>RUNNING</installStateName>'
    create: yes
    owner: "{{ jenkins_user }}"
    group: "{{ jenkins_group }}"
  notify: restart jenkins
  tags: config

- name: Create <PERSON> admin user
  template:
    src: users/admin/config.xml.j2
    dest: "{{ jenkins_home }}/users/{{ jenkins_admin_username }}/config.xml"
    owner: "{{ jenkins_user }}"
    group: "{{ jenkins_group }}"
    mode: '0644'
  notify: restart jenkins
  tags: admin_user

- name: Configure <PERSON> security
  template:
    src: config.xml.j2
    dest: "{{ jenkins_home }}/config.xml"
    owner: "{{ jenkins_user }}"
    group: "{{ jenkins_group }}"
    mode: '0644'
    backup: yes
  notify: restart jenkins
  tags: security

- name: Configure Jenkins location
  template:
    src: jenkins.model.JenkinsLocationConfiguration.xml.j2
    dest: "{{ jenkins_home }}/jenkins.model.JenkinsLocationConfiguration.xml"
    owner: "{{ jenkins_user }}"
    group: "{{ jenkins_group }}"
    mode: '0644'
  notify: restart jenkins
  tags: location

- name: Configure Jenkins email
  template:
    src: hudson.tasks.Mailer.xml.j2
    dest: "{{ jenkins_home }}/hudson.tasks.Mailer.xml"
    owner: "{{ jenkins_user }}"
    group: "{{ jenkins_group }}"
    mode: '0644'
  when: jenkins_email_enabled
  notify: restart jenkins
  tags: email
