---
# <PERSON> installation tasks

- name: <PERSON><PERSON> repository key (Debian/Ubuntu)
  apt_key:
    url: https://pkg.jenkins.io/debian-stable/jenkins.io-2023.key
    state: present
  when: ansible_os_family == "Debian"
  tags: repository

- name: Add Jenkins repository (Debian/Ubuntu)
  apt_repository:
    repo: "deb https://pkg.jenkins.io/debian-stable binary/"
    state: present
    update_cache: yes
  when: ansible_os_family == "Debian"
  tags: repository

- name: Add Jenkins repository (RedHat/CentOS)
  yum_repository:
    name: jenkins
    description: Jenkins-stable
    baseurl: http://pkg.jenkins.io/redhat-stable
    gpgcheck: yes
    gpgkey: https://pkg.jenkins.io/redhat-stable/jenkins.io-2023.key
    enabled: yes
  when: ansible_os_family == "RedHat"
  tags: repository

- name: Install Jenkins
  package:
    name: jenkins
    state: "{{ jenkins_package_state }}"
  notify: restart jenkins
  tags: package

- name: Create <PERSON> user
  user:
    name: "{{ jenkins_user }}"
    group: "{{ jen<PERSON>_group }}"
    home: "{{ jenkins_home }}"
    shell: /bin/bash
    system: yes
  tags: user

- name: <PERSON>reate <PERSON> directories
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ jenkins_user }}"
    group: "{{ jenkins_group }}"
    mode: '0755'
  with_items:
    - "{{ jenkins_home }}"
    - "{{ jenkins_home }}/plugins"
    - "{{ jenkins_home }}/jobs"
    - "{{ jenkins_casc_config_path }}"
    - "{{ jenkins_backup_path }}"
  tags: directories

- name: Set Jenkins home ownership
  file:
    path: "{{ jenkins_home }}"
    owner: "{{ jenkins_user }}"
    group: "{{ jenkins_group }}"
    recurse: yes
  tags: permissions
