# Jenkins configuration file
# Managed by Ansible

# Jenkins home directory
JENKINS_HOME="{{ jenkins_home }}"

# Jenkins user
JENKINS_USER="{{ jenkins_user }}"

# Jenkins group
JENKINS_GROUP="{{ jenkins_group }}"

# <PERSON> port
HTTP_PORT="{{ jenkins_port }}"

# Jenkins host
HTTP_HOST="{{ jenkins_host }}"

# Java options
JAVA_ARGS="{{ jenkins_java_options }}"

# Jenkins arguments
JENKINS_ARGS="--webroot=/var/cache/jenkins/war --httpPort=$HTTP_PORT --httpListenAddress=$HTTP_HOST"

# JVM heap size
JAVA_OPTS="-Xmx{{ jenkins_java_heap_size }} -Xms{{ jenkins_java_heap_size }}"

# Additional Java options
JAVA_OPTS="$JAVA_OPTS -Djava.awt.headless=true"
JAVA_OPTS="$JAVA_OPTS -Djenkins.install.runSetupWizard=false"
JAVA_OPTS="$JAVA_OPTS -Dhudson.security.csrf.DefaultCrumbIssuer.EXCLUDE_SESSION_ID=true"

{% if jenkins_casc_enabled %}
# Jenkins Configuration as Code
CASC_JENKINS_CONFIG="{{ jenkins_casc_config_path }}"
{% endif %}

# Plugin directory
JENKINS_PLUGIN_DIR="{{ jenkins_home }}/plugins"

# Log directory
JENKINS_LOG="/var/log/jenkins/jenkins.log"

# PID file
PIDFILE="/var/run/jenkins/jenkins.pid"
