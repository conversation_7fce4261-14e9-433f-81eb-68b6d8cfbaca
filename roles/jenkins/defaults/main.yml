---
# <PERSON> role default variables

# <PERSON> installation
jenkins_version: "2.414.1"
jenkins_package_state: present
jenkins_home: /var/lib/jenkins
jenkins_user: jenkins
jenkins_group: jenkins
jenkins_port: 8080
jenkins_host: 0.0.0.0
jenkins_url_prefix: ""

# Java configuration
jenkins_java_options: "-Djenkins.install.runSetupWizard=false -Djava.awt.headless=true"
jenkins_java_heap_size: "1g"

# Security configuration
jenkins_admin_username: admin
jenkins_admin_password: "{{ vault_jenkins_admin_password | default('admin123') }}"
jenkins_security_realm: "jenkins"
jenkins_authorization_strategy: "loggedInUsersCanDoAnything"

# Plugin management
jenkins_plugins_install_dependencies: true
jenkins_plugins_state: present
jenkins_plugin_updates_expiration: 86400
jenkins_plugin_timeout: 30

# Essential plugins
jenkins_plugins:
  - ant
  - antisamy-markup-formatter
  - build-timeout
  - credentials-binding
  - email-ext
  - git
  - github-branch-source
  - gradle
  - ldap
  - mailer
  - matrix-auth
  - pam-auth
  - pipeline-github-lib
  - pipeline-stage-view
  - ssh-slaves
  - timestamper
  - workflow-aggregator
  - ws-cleanup
  - blueocean
  - docker-workflow
  - kubernetes
  - ansible
  - sonar
  - nexus-artifact-uploader

# Jenkins configuration as code (JCasC)
jenkins_casc_enabled: true
jenkins_casc_config_path: "{{ jenkins_home }}/casc_configs"

# Backup configuration
jenkins_backup_enabled: true
jenkins_backup_path: "/opt/backups/jenkins"
jenkins_backup_schedule: "0 2 * * *"  # Daily at 2 AM

# SSL configuration
jenkins_ssl_enabled: false
jenkins_ssl_certificate: /etc/ssl/certs/jenkins.crt
jenkins_ssl_private_key: /etc/ssl/private/jenkins.key

# Reverse proxy configuration
jenkins_reverse_proxy_enabled: false
jenkins_reverse_proxy_host: jenkins.example.com

# Agent configuration
jenkins_agents_enabled: true
jenkins_agent_port: 50000

# Pipeline libraries
jenkins_shared_libraries:
  - name: "shared-library"
    scm: "git"
    url: "https://github.com/your-org/jenkins-shared-library.git"
    default_version: "main"

# Email configuration
jenkins_email_enabled: true
jenkins_smtp_server: "smtp.example.com"
jenkins_smtp_port: 587
jenkins_smtp_username: "<EMAIL>"
jenkins_smtp_password: "{{ vault_jenkins_smtp_password | default('') }}"

# System configuration
jenkins_system_message: "Jenkins Automation Server - Managed by Ansible"
jenkins_num_executors: 2
jenkins_quiet_period: 5
jenkins_scm_checkout_retry_count: 0

# Tool installations
jenkins_tools:
  maven:
    - name: "Maven-3.8.6"
      version: "3.8.6"
  gradle:
    - name: "Gradle-7.5"
      version: "7.5"
  nodejs:
    - name: "NodeJS-18"
      version: "18.17.0"
  docker:
    - name: "Docker"
      version: "latest"
