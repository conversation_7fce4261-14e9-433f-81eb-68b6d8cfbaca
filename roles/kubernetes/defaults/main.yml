---
# Kubernetes role default variables

# Kubernetes version
kubernetes_version: "1.28.2"
kubernetes_cni_version: "1.3.0"
kubernetes_cri_version: "1.28.0"

# Cluster configuration
kubernetes_cluster_name: "devops-cluster"
kubernetes_pod_subnet: "**********/16"
kubernetes_service_subnet: "*********/12"
kubernetes_dns_domain: "cluster.local"

# Master node configuration
kubernetes_master_node: true
kubernetes_worker_node: false
kubernetes_control_plane_endpoint: "{{ ansible_default_ipv4.address }}:6443"

# Container runtime
kubernetes_container_runtime: "containerd"  # Options: docker, containerd, cri-o
containerd_version: "1.7.5"

# CNI Plugin
kubernetes_cni_plugin: "flannel"  # Options: flannel, calico, weave, cilium
calico_version: "3.26.1"
flannel_version: "0.22.2"

# Helm configuration
helm_enabled: true
helm_version: "3.12.3"
helm_install_path: "/usr/local/bin"

# Ingress controller
ingress_controller_enabled: true
ingress_controller_type: "nginx"  # Options: nginx, traefik, istio
nginx_ingress_version: "1.8.2"

# Service mesh
service_mesh_enabled: false
service_mesh_type: "istio"  # Options: istio, linkerd, consul-connect
istio_version: "1.19.0"

# Storage
storage_class_enabled: true
default_storage_class: "local-path"
nfs_storage_enabled: false
nfs_server: ""
nfs_path: "/var/nfs"

# Monitoring
kubernetes_monitoring_enabled: true
prometheus_operator_enabled: true
grafana_enabled: true

# Dashboard
kubernetes_dashboard_enabled: true
dashboard_version: "2.7.0"

# RBAC
rbac_enabled: true
pod_security_policy_enabled: false

# Network policies
network_policies_enabled: false

# Backup
etcd_backup_enabled: true
etcd_backup_schedule: "0 2 * * *"
etcd_backup_retention: "30"

# Security
kubernetes_audit_enabled: true
kubernetes_encryption_enabled: true
admission_controllers:
  - NamespaceLifecycle
  - LimitRanger
  - ServiceAccount
  - DefaultStorageClass
  - DefaultTolerationSeconds
  - MutatingAdmissionWebhook
  - ValidatingAdmissionWebhook
  - ResourceQuota
  - NodeRestriction

# Certificates
cert_manager_enabled: true
cert_manager_version: "1.13.1"
letsencrypt_enabled: true
letsencrypt_email: "<EMAIL>"

# Registry
private_registry_enabled: false
private_registry_url: "registry.example.com"
private_registry_username: ""
private_registry_password: "{{ vault_registry_password | default('') }}"

# Namespaces to create
kubernetes_namespaces:
  - name: "development"
    labels:
      environment: "dev"
  - name: "staging"
    labels:
      environment: "staging"
  - name: "production"
    labels:
      environment: "prod"
  - name: "monitoring"
    labels:
      purpose: "monitoring"
  - name: "ingress-nginx"
    labels:
      purpose: "ingress"
