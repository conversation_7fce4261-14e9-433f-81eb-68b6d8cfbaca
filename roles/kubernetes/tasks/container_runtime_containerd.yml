---
# Install and configure containerd

- name: Install containerd dependencies
  package:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - gnupg
      - lsb-release
    state: present
  when: ansible_os_family == "Debian"
  tags: containerd_deps

- name: Add Docker GPG key
  apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg
    state: present
  when: ansible_os_family == "Debian"
  tags: containerd_repo

- name: Add Docker repository
  apt_repository:
    repo: "deb [arch=amd64] https://download.docker.com/linux/ubuntu {{ ansible_distribution_release }} stable"
    state: present
    update_cache: yes
  when: ansible_os_family == "Debian"
  tags: containerd_repo

- name: Install containerd
  package:
    name: containerd.io
    state: present
  tags: containerd_install

- name: Create containerd configuration directory
  file:
    path: /etc/containerd
    state: directory
    mode: '0755'
  tags: containerd_config

- name: Generate default containerd configuration
  shell: containerd config default > /etc/containerd/config.toml
  args:
    creates: /etc/containerd/config.toml
  tags: containerd_config

- name: Configure containerd to use systemd cgroup driver
  lineinfile:
    path: /etc/containerd/config.toml
    regexp: '^\s*SystemdCgroup\s*='
    line: '            SystemdCgroup = true'
    insertafter: '^\s*\[plugins\."io\.containerd\.grpc\.v1\.cri"\.containerd\.runtimes\.runc\.options\]'
  notify: restart containerd
  tags: containerd_config

- name: Start and enable containerd service
  systemd:
    name: containerd
    state: started
    enabled: yes
    daemon_reload: yes
  tags: containerd_service
