---
# Configure kernel modules for Kubernetes

- name: Load required kernel modules
  modprobe:
    name: "{{ item }}"
    state: present
  with_items:
    - br_netfilter
    - overlay
    - ip_vs
    - ip_vs_rr
    - ip_vs_wrr
    - ip_vs_sh
    - nf_conntrack
  tags: kernel_modules

- name: Ensure kernel modules are loaded on boot
  lineinfile:
    path: /etc/modules-load.d/kubernetes.conf
    line: "{{ item }}"
    create: yes
    mode: '0644'
  with_items:
    - br_netfilter
    - overlay
    - ip_vs
    - ip_vs_rr
    - ip_vs_wrr
    - ip_vs_sh
    - nf_conntrack
  tags: kernel_modules

- name: Configure sysctl parameters for Kubernetes
  sysctl:
    name: "{{ item.name }}"
    value: "{{ item.value }}"
    state: present
    reload: yes
    sysctl_file: /etc/sysctl.d/kubernetes.conf
  with_items:
    - { name: "net.bridge.bridge-nf-call-iptables", value: "1" }
    - { name: "net.bridge.bridge-nf-call-ip6tables", value: "1" }
    - { name: "net.ipv4.ip_forward", value: "1" }
    - { name: "net.ipv4.conf.all.forwarding", value: "1" }
    - { name: "net.ipv6.conf.all.forwarding", value: "1" }
    - { name: "net.netfilter.nf_conntrack_max", value: "1000000" }
    - { name: "vm.swappiness", value: "0" }
    - { name: "vm.overcommit_memory", value: "1" }
    - { name: "vm.panic_on_oom", value: "0" }
    - { name: "fs.inotify.max_user_instances", value: "8192" }
    - { name: "fs.inotify.max_user_watches", value: "1048576" }
    - { name: "fs.file-max", value: "2097152" }
    - { name: "fs.nr_open", value: "1048576" }
  tags: kernel_params
