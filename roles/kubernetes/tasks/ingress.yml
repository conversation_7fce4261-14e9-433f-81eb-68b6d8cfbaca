---
# Install ingress controller

- name: Install NGINX Ingress Controller
  kubernetes.core.helm:
    name: ingress-nginx
    chart_ref: ingress-nginx/ingress-nginx
    release_namespace: ingress-nginx
    create_namespace: true
    values:
      controller:
        service:
          type: LoadBalancer
        metrics:
          enabled: true
        podAnnotations:
          prometheus.io/scrape: "true"
          prometheus.io/port: "10254"
        config:
          use-forwarded-headers: "true"
          compute-full-forwarded-for: "true"
          use-proxy-protocol: "false"
        resources:
          requests:
            cpu: 100m
            memory: 90Mi
          limits:
            cpu: 200m
            memory: 256Mi
  when: ingress_controller_type == "nginx"
  tags: ingress

- name: Wait for NGINX Ingress Controller to be ready
  kubernetes.core.k8s_info:
    api_version: apps/v1
    kind: Deployment
    name: ingress-nginx-controller
    namespace: ingress-nginx
    wait: true
    wait_condition:
      type: Available
      status: "True"
    wait_timeout: 300
  when: ingress_controller_type == "nginx"
  tags: ingress

- name: Get ingress controller service
  kubernetes.core.k8s_info:
    api_version: v1
    kind: Service
    name: ingress-nginx-controller
    namespace: ingress-nginx
  register: ingress_service
  when: ingress_controller_type == "nginx"
  tags: ingress

- name: Display ingress controller information
  debug:
    msg: |
      Ingress Controller Status:
      - Type: {{ ingress_controller_type }}
      - Namespace: ingress-nginx
      - Service Type: {{ ingress_service.resources[0].spec.type }}
      {% if ingress_service.resources[0].status.loadBalancer.ingress is defined %}
      - External IP: {{ ingress_service.resources[0].status.loadBalancer.ingress[0].ip | default('Pending') }}
      {% endif %}
  when: ingress_controller_type == "nginx"
  tags: ingress
