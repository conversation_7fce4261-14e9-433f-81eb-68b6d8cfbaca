---
# Install <PERSON>lm package manager

- name: Check if <PERSON><PERSON> is already installed
  command: helm version --short
  register: helm_installed
  changed_when: false
  failed_when: false
  tags: helm

- name: Download Helm installation script
  get_url:
    url: https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3
    dest: /tmp/get_helm.sh
    mode: '0755'
  when: helm_installed.rc != 0
  tags: helm

- name: Install Helm
  command: /tmp/get_helm.sh
  when: helm_installed.rc != 0
  tags: helm

- name: Verify Helm installation
  command: helm version --short
  register: helm_version
  changed_when: false
  tags: helm

- name: Display Helm version
  debug:
    var: helm_version.stdout
  tags: helm

- name: Add stable Helm repository
  kubernetes.core.helm_repository:
    name: stable
    repo_url: https://charts.helm.sh/stable
  tags: helm_repos

- name: Add bitnami Helm repository
  kubernetes.core.helm_repository:
    name: bitnami
    repo_url: https://charts.bitnami.com/bitnami
  tags: helm_repos

- name: Add ingress-nginx Helm repository
  kubernetes.core.helm_repository:
    name: ingress-nginx
    repo_url: https://kubernetes.github.io/ingress-nginx
  tags: helm_repos

- name: Add prometheus-community Helm repository
  kubernetes.core.helm_repository:
    name: prometheus-community
    repo_url: https://prometheus-community.github.io/helm-charts
  tags: helm_repos

- name: Update Helm repositories
  kubernetes.core.helm:
    name: dummy
    chart_ref: stable/dummy
    state: absent
    update_repo_cache: yes
  ignore_errors: yes
  tags: helm_repos
