---
# Initialize Kubernetes master node

- name: Check if Kubernetes is already initialized
  stat:
    path: /etc/kubernetes/admin.conf
  register: k8s_admin_conf
  tags: k8s_init

- name: Create kubeadm configuration
  template:
    src: kubeadm-config.yml.j2
    dest: /etc/kubernetes/kubeadm-config.yml
    mode: '0644'
  when: not k8s_admin_conf.stat.exists
  tags: k8s_init

- name: Initialize Kubernetes cluster
  command: >
    kubeadm init
    --config=/etc/kubernetes/kubeadm-config.yml
    --upload-certs
  register: kubeadm_init
  when: not k8s_admin_conf.stat.exists
  tags: k8s_init

- name: Create .kube directory for root
  file:
    path: /root/.kube
    state: directory
    mode: '0755'
  tags: k8s_config

- name: Copy admin.conf to root's kube config
  copy:
    src: /etc/kubernetes/admin.conf
    dest: /root/.kube/config
    remote_src: yes
    owner: root
    group: root
    mode: '0644'
  tags: k8s_config

- name: Create .kube directory for regular user
  file:
    path: "/home/<USER>/.kube"
    state: directory
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: '0755'
  when: ansible_user != "root"
  tags: k8s_config

- name: Copy admin.conf to user's kube config
  copy:
    src: /etc/kubernetes/admin.conf
    dest: "/home/<USER>/.kube/config"
    remote_src: yes
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: '0644'
  when: ansible_user != "root"
  tags: k8s_config

- name: Wait for Kubernetes API server to be ready
  wait_for:
    port: 6443
    host: "{{ ansible_default_ipv4.address }}"
    delay: 10
    timeout: 300
  tags: k8s_init

- name: Remove taint from master node (for single-node clusters)
  command: kubectl taint nodes --all node-role.kubernetes.io/control-plane-
  ignore_errors: yes
  when: kubernetes_single_node | default(false)
  tags: k8s_taint
