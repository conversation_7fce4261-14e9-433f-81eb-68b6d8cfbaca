---
# Kubernetes role main tasks

- name: Include OS-specific variables
  include_vars: "{{ ansible_os_family }}.yml"
  tags: always

- name: Disable swap
  include_tasks: disable_swap.yml
  tags: swap

- name: Configure kernel modules
  include_tasks: kernel_modules.yml
  tags: kernel

- name: Install container runtime
  include_tasks: "container_runtime_{{ kubernetes_container_runtime }}.yml"
  tags: container_runtime

- name: Install Kubernetes packages
  include_tasks: install_kubernetes.yml
  tags: install

- name: Initialize Kubernetes master
  include_tasks: master_init.yml
  when: kubernetes_master_node
  tags: master

- name: Join worker nodes
  include_tasks: worker_join.yml
  when: kubernetes_worker_node
  tags: worker

- name: Install CNI plugin
  include_tasks: "cni_{{ kubernetes_cni_plugin }}.yml"
  when: kubernetes_master_node
  tags: cni

- name: Install Helm
  include_tasks: helm.yml
  when: helm_enabled and kubernetes_master_node
  tags: helm

- name: Install ingress controller
  include_tasks: ingress.yml
  when: ingress_controller_enabled and kubernetes_master_node
  tags: ingress

- name: Install service mesh
  include_tasks: "service_mesh_{{ service_mesh_type }}.yml"
  when: service_mesh_enabled and kubernetes_master_node
  tags: service_mesh

- name: Configure storage classes
  include_tasks: storage.yml
  when: storage_class_enabled and kubernetes_master_node
  tags: storage

- name: Install cert-manager
  include_tasks: cert_manager.yml
  when: cert_manager_enabled and kubernetes_master_node
  tags: cert_manager

- name: Install Kubernetes dashboard
  include_tasks: dashboard.yml
  when: kubernetes_dashboard_enabled and kubernetes_master_node
  tags: dashboard

- name: Create namespaces
  include_tasks: namespaces.yml
  when: kubernetes_master_node
  tags: namespaces

- name: Configure monitoring
  include_tasks: monitoring.yml
  when: kubernetes_monitoring_enabled and kubernetes_master_node
  tags: monitoring

- name: Configure backup
  include_tasks: backup.yml
  when: etcd_backup_enabled and kubernetes_master_node
  tags: backup
