---
# Disable swap for Kubernetes

- name: Check if swap is enabled
  command: swapon --show
  register: swap_status
  changed_when: false
  failed_when: false
  tags: swap

- name: Disable swap immediately
  command: swapoff -a
  when: swap_status.stdout != ""
  tags: swap

- name: Remove swap entries from /etc/fstab
  lineinfile:
    path: /etc/fstab
    regexp: '.*swap.*'
    state: absent
    backup: yes
  tags: swap

- name: Ensure swap is disabled on boot
  systemd:
    name: swap.target
    masked: yes
  ignore_errors: yes
  tags: swap
