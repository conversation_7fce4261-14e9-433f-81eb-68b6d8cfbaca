---
# Install Flannel CNI plugin

- name: Download Flannel manifest
  get_url:
    url: "https://github.com/flannel-io/flannel/releases/download/v{{ flannel_version }}/kube-flannel.yml"
    dest: /tmp/kube-flannel.yml
    mode: '0644'
  tags: flannel

- name: Modify Flannel manifest for custom pod subnet
  replace:
    path: /tmp/kube-flannel.yml
    regexp: '10\.244\.0\.0/16'
    replace: "{{ kubernetes_pod_subnet }}"
  when: kubernetes_pod_subnet != "10.244.0.0/16"
  tags: flannel

- name: Apply Flannel CNI
  kubernetes.core.k8s:
    src: /tmp/kube-flannel.yml
    state: present
    wait: true
    wait_timeout: 300
  tags: flannel

- name: Wait for Flannel pods to be ready
  kubernetes.core.k8s_info:
    api_version: v1
    kind: Pod
    namespace: kube-flannel
    label_selectors:
      - app=flannel
    wait: true
    wait_condition:
      type: Ready
      status: "True"
    wait_timeout: 300
  tags: flannel

- name: Verify Flannel installation
  kubernetes.core.k8s_info:
    api_version: v1
    kind: Pod
    namespace: kube-flannel
  register: flannel_pods
  tags: flannel

- name: Display Flannel pod status
  debug:
    msg: "Flannel pods: {{ flannel_pods.resources | length }} running"
  tags: flannel
