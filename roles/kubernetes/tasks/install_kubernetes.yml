---
# Install Kubernetes packages

- name: Add Kubernetes GPG key
  apt_key:
    url: https://packages.cloud.google.com/apt/doc/apt-key.gpg
    state: present
  when: ansible_os_family == "Debian"
  tags: k8s_repo

- name: Add Kubernetes repository
  apt_repository:
    repo: "deb https://apt.kubernetes.io/ kubernetes-xenial main"
    state: present
    update_cache: yes
  when: ansible_os_family == "Debian"
  tags: k8s_repo

- name: Install Kubernetes packages
  package:
    name:
      - "kubelet={{ kubernetes_version }}-00"
      - "kubeadm={{ kubernetes_version }}-00"
      - "kubectl={{ kubernetes_version }}-00"
    state: present
    allow_downgrade: yes
  when: ansible_os_family == "Debian"
  tags: k8s_install

- name: Hold Kubernetes packages
  dpkg_selections:
    name: "{{ item }}"
    selection: hold
  with_items:
    - kubelet
    - kubeadm
    - kubectl
  when: ansible_os_family == "Debian"
  tags: k8s_install

- name: Configure kubelet
  template:
    src: kubelet-config.yml.j2
    dest: /etc/kubernetes/kubelet-config.yml
    mode: '0644'
  notify: restart kubelet
  tags: kubelet_config

- name: Create kubelet systemd drop-in directory
  file:
    path: /etc/systemd/system/kubelet.service.d
    state: directory
    mode: '0755'
  tags: kubelet_config

- name: Configure kubelet systemd service
  template:
    src: 10-kubeadm.conf.j2
    dest: /etc/systemd/system/kubelet.service.d/10-kubeadm.conf
    mode: '0644'
  notify:
    - reload systemd
    - restart kubelet
  tags: kubelet_config

- name: Start and enable kubelet service
  systemd:
    name: kubelet
    state: started
    enabled: yes
    daemon_reload: yes
  tags: kubelet_service
