---
# Join worker nodes to Kubernetes cluster

- name: Check if node is already part of cluster
  stat:
    path: /etc/kubernetes/kubelet.conf
  register: kubelet_conf
  tags: k8s_join

- name: Read join command from file
  slurp:
    src: /tmp/kubernetes_join_command
  register: join_command_b64
  when: not kubelet_conf.stat.exists
  tags: k8s_join

- name: Decode join command
  set_fact:
    join_command: "{{ join_command_b64.content | b64decode | trim }}"
  when: not kubelet_conf.stat.exists
  tags: k8s_join

- name: Join worker node to cluster
  command: "{{ join_command }}"
  when: not kubelet_conf.stat.exists
  tags: k8s_join

- name: Wait for node to be ready
  wait_for:
    timeout: 300
  when: not kubelet_conf.stat.exists
  tags: k8s_join

- name: Verify node joined cluster
  command: kubectl get nodes
  register: cluster_nodes
  delegate_to: "{{ groups['kubernetes_masters'][0] }}"
  tags: k8s_verify

- name: Display cluster nodes
  debug:
    var: cluster_nodes.stdout_lines
  tags: k8s_verify
