apiVersion: kubeadm.k8s.io/v1beta3
kind: InitConfiguration
localAPIEndpoint:
  advertiseAddress: {{ ansible_default_ipv4.address }}
  bindPort: 6443
nodeRegistration:
  criSocket: unix:///var/run/containerd/containerd.sock
  kubeletExtraArgs:
    cloud-provider: external
---
apiVersion: kubeadm.k8s.io/v1beta3
kind: ClusterConfiguration
kubernetesVersion: v{{ kubernetes_version }}
clusterName: {{ kubernetes_cluster_name }}
controlPlaneEndpoint: {{ kubernetes_control_plane_endpoint }}
apiServer:
  advertiseAddress: {{ ansible_default_ipv4.address }}
  bindPort: 6443
  extraArgs:
    audit-log-maxage: "30"
    audit-log-maxbackup: "3"
    audit-log-maxsize: "100"
    audit-log-path: /var/log/audit.log
    enable-admission-plugins: {{ admission_controllers | join(',') }}
{% if kubernetes_audit_enabled %}
    audit-policy-file: /etc/kubernetes/audit-policy.yaml
{% endif %}
  extraVolumes:
  - name: audit-log
    hostPath: /var/log/audit.log
    mountPath: /var/log/audit.log
    pathType: FileOrCreate
{% if kubernetes_audit_enabled %}
  - name: audit-policy
    hostPath: /etc/kubernetes/audit-policy.yaml
    mountPath: /etc/kubernetes/audit-policy.yaml
    pathType: File
    readOnly: true
{% endif %}
controllerManager:
  extraArgs:
    bind-address: 0.0.0.0
scheduler:
  extraArgs:
    bind-address: 0.0.0.0
etcd:
  local:
    dataDir: /var/lib/etcd
    extraArgs:
      listen-metrics-urls: http://0.0.0.0:2381
networking:
  serviceSubnet: {{ kubernetes_service_subnet }}
  podSubnet: {{ kubernetes_pod_subnet }}
  dnsDomain: {{ kubernetes_dns_domain }}
---
apiVersion: kubelet.config.k8s.io/v1beta1
kind: KubeletConfiguration
cgroupDriver: systemd
failSwapOn: false
serverTLSBootstrap: true
rotateCertificates: true
---
apiVersion: kubeproxy.config.k8s.io/v1alpha1
kind: KubeProxyConfiguration
mode: ipvs
ipvs:
  strictARP: true
