---
# Apache Kafka role default variables

# Kafka version and installation
kafka_version: "2.13-3.5.1"
kafka_scala_version: "2.13"
kafka_package_state: present
kafka_user: kafka
kafka_group: kafka
kafka_home: /opt/kafka
kafka_config_dir: "{{ kafka_home }}/config"
kafka_log_dir: /var/log/kafka
kafka_data_dir: /var/lib/kafka

# Network configuration
kafka_listeners: "PLAINTEXT://0.0.0.0:9092"
kafka_advertised_listeners: "PLAINTEXT://{{ ansible_default_ipv4.address }}:9092"
kafka_listener_security_protocol_map: "PLAINTEXT:PLAINTEXT"
kafka_inter_broker_listener_name: PLAINTEXT
kafka_port: 9092

# Zookeeper configuration
kafka_zookeeper_enabled: true
kafka_zookeeper_connect: "localhost:2181"
kafka_zookeeper_connection_timeout_ms: 18000
kafka_zookeeper_session_timeout_ms: 18000

# Broker configuration
kafka_broker_id: "{{ groups['kafka'] | default(['localhost']) | list | index(inventory_hostname) + 1 }}"
kafka_num_network_threads: 3
kafka_num_io_threads: 8
kafka_socket_send_buffer_bytes: 102400
kafka_socket_receive_buffer_bytes: 102400
kafka_socket_request_max_bytes: 104857600

# Log configuration
kafka_log_dirs: "{{ kafka_data_dir }}/kafka-logs"
kafka_num_partitions: 3
kafka_num_recovery_threads_per_data_dir: 1
kafka_offsets_topic_replication_factor: 3
kafka_transaction_state_log_replication_factor: 3
kafka_transaction_state_log_min_isr: 2
kafka_default_replication_factor: 3
kafka_min_insync_replicas: 2

# Log retention configuration
kafka_log_retention_hours: 168  # 7 days
kafka_log_retention_bytes: -1   # No size limit
kafka_log_segment_bytes: 1073741824  # 1GB
kafka_log_retention_check_interval_ms: 300000
kafka_log_cleanup_policy: delete

# Compression
kafka_compression_type: producer  # none, gzip, snappy, lz4, zstd, producer

# Replication configuration
kafka_replica_lag_time_max_ms: 30000
kafka_replica_socket_timeout_ms: 30000
kafka_replica_socket_receive_buffer_bytes: 65536
kafka_replica_fetch_max_bytes: 1048576
kafka_replica_fetch_wait_max_ms: 500
kafka_replica_fetch_min_bytes: 1

# Producer configuration
kafka_producer_batch_size: 16384
kafka_producer_linger_ms: 0
kafka_producer_buffer_memory: 33554432
kafka_producer_acks: all
kafka_producer_retries: 2147483647
kafka_producer_max_in_flight_requests_per_connection: 5
kafka_producer_enable_idempotence: true

# Consumer configuration
kafka_consumer_fetch_min_bytes: 1
kafka_consumer_fetch_max_wait_ms: 500
kafka_consumer_max_partition_fetch_bytes: 1048576
kafka_consumer_auto_offset_reset: latest
kafka_consumer_enable_auto_commit: true
kafka_consumer_auto_commit_interval_ms: 5000

# Security configuration
kafka_security_enabled: false
kafka_ssl_enabled: false
kafka_ssl_keystore_location: "{{ kafka_config_dir }}/kafka.server.keystore.jks"
kafka_ssl_keystore_password: "{{ vault_kafka_ssl_keystore_password }}"
kafka_ssl_key_password: "{{ vault_kafka_ssl_key_password }}"
kafka_ssl_truststore_location: "{{ kafka_config_dir }}/kafka.server.truststore.jks"
kafka_ssl_truststore_password: "{{ vault_kafka_ssl_truststore_password }}"

# SASL configuration
kafka_sasl_enabled: false
kafka_sasl_mechanism: PLAIN
kafka_sasl_protocol: SASL_PLAINTEXT
kafka_sasl_users:
  - username: admin
    password: "{{ vault_kafka_admin_password }}"
  - username: producer
    password: "{{ vault_kafka_producer_password }}"
  - username: consumer
    password: "{{ vault_kafka_consumer_password }}"

# JVM configuration
kafka_heap_size: "1G"
kafka_jvm_opts:
  - "-Xms{{ kafka_heap_size }}"
  - "-Xmx{{ kafka_heap_size }}"
  - "-XX:+UseG1GC"
  - "-XX:MaxGCPauseMillis=20"
  - "-XX:InitiatingHeapOccupancyPercent=35"
  - "-XX:+ExplicitGCInvokesConcurrent"
  - "-Djava.awt.headless=true"
  - "-Dcom.sun.management.jmxremote"
  - "-Dcom.sun.management.jmxremote.authenticate=false"
  - "-Dcom.sun.management.jmxremote.ssl=false"
  - "-Dcom.sun.management.jmxremote.port=9999"

# Monitoring configuration
kafka_monitoring_enabled: true
kafka_jmx_enabled: true
kafka_jmx_port: 9999
kafka_exporter_enabled: true
kafka_exporter_port: 9308

# Topics to create
kafka_topics:
  - name: test-topic
    partitions: 3
    replication_factor: 3
    config:
      cleanup.policy: delete
      retention.ms: 604800000  # 7 days

# Connect configuration
kafka_connect_enabled: false
kafka_connect_port: 8083
kafka_connect_group_id: connect-cluster
kafka_connect_config_storage_topic: connect-configs
kafka_connect_offset_storage_topic: connect-offsets
kafka_connect_status_storage_topic: connect-status

# Schema Registry configuration
kafka_schema_registry_enabled: false
kafka_schema_registry_port: 8081
kafka_schema_registry_kafkastore_connection_url: "{{ kafka_zookeeper_connect }}"

# KSQL configuration
kafka_ksql_enabled: false
kafka_ksql_port: 8088
kafka_ksql_bootstrap_servers: "{{ kafka_advertised_listeners }}"

# Backup configuration
kafka_backup_enabled: true
kafka_backup_dir: /opt/kafka-backups
kafka_backup_schedule: "0 2 * * *"
kafka_backup_retention_days: 7

# Custom configuration
kafka_custom_server_config: {}
kafka_custom_producer_config: {}
kafka_custom_consumer_config: {}

# Firewall configuration
kafka_firewall_enabled: true
kafka_firewall_allowed_hosts:
  - "10.0.0.0/8"
  - "**********/12"
  - "***********/16"
