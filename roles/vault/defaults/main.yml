---
# Has<PERSON><PERSON><PERSON><PERSON> Vault role default variables

# Vault installation
vault_version: "1.15.1"
vault_package_state: present
vault_user: vault
vault_group: vault
vault_home: /opt/vault
vault_data_dir: /opt/vault/data
vault_config_dir: /etc/vault.d
vault_log_dir: /var/log/vault

# Network configuration
vault_port: 8200
vault_cluster_port: 8201
vault_address: "{{ ansible_default_ipv4.address }}"
vault_api_addr: "http://{{ vault_address }}:{{ vault_port }}"
vault_cluster_addr: "https://{{ vault_address }}:{{ vault_cluster_port }}"

# TLS configuration
vault_tls_enabled: true
vault_tls_cert_file: "{{ vault_config_dir }}/vault.crt"
vault_tls_key_file: "{{ vault_config_dir }}/vault.key"
vault_tls_ca_file: "{{ vault_config_dir }}/ca.crt"

# Storage backend
vault_storage_backend: "file"  # Options: file, consul, etcd, s3, gcs
vault_storage_config:
  file:
    path: "{{ vault_data_dir }}"
  consul:
    address: "127.0.0.1:8500"
    path: "vault/"
  etcd:
    address: "http://127.0.0.1:2379"
    etcd_api: "v3"

# High Availability
vault_ha_enabled: false
vault_cluster_name: "vault-cluster"

# Auto-unseal (for cloud deployments)
vault_auto_unseal_enabled: false
vault_auto_unseal_type: "aws"  # Options: aws, azure, gcp
vault_auto_unseal_config: {}

# Initialization
vault_init_enabled: true
vault_key_shares: 5
vault_key_threshold: 3
vault_recovery_shares: 5
vault_recovery_threshold: 3

# Authentication methods
vault_auth_methods:
  - type: "userpass"
    path: "userpass"
    description: "Username and password authentication"
  - type: "ldap"
    path: "ldap"
    description: "LDAP authentication"
    config:
      url: "ldap://ldap.example.com"
      userdn: "ou=users,dc=example,dc=com"
      groupdn: "ou=groups,dc=example,dc=com"
      binddn: "cn=vault,ou=service,dc=example,dc=com"
      bindpass: "{{ vault_ldap_bind_password }}"
  - type: "kubernetes"
    path: "kubernetes"
    description: "Kubernetes service account authentication"

# Secret engines
vault_secret_engines:
  - type: "kv-v2"
    path: "secret"
    description: "Key-Value secrets engine"
  - type: "database"
    path: "database"
    description: "Database secrets engine"
  - type: "pki"
    path: "pki"
    description: "PKI secrets engine"
    config:
      max_lease_ttl: "8760h"  # 1 year
  - type: "transit"
    path: "transit"
    description: "Transit secrets engine for encryption"

# Policies
vault_policies:
  - name: "admin"
    policy: |
      path "*" {
        capabilities = ["create", "read", "update", "delete", "list", "sudo"]
      }
  - name: "developer"
    policy: |
      path "secret/data/dev/*" {
        capabilities = ["create", "read", "update", "delete", "list"]
      }
      path "secret/metadata/dev/*" {
        capabilities = ["list"]
      }
  - name: "ci-cd"
    policy: |
      path "secret/data/ci/*" {
        capabilities = ["read"]
      }
      path "database/creds/app-role" {
        capabilities = ["read"]
      }

# Users (for userpass auth)
vault_users:
  - username: "admin"
    password: "{{ vault_admin_password }}"
    policies: ["admin"]
  - username: "developer"
    password: "{{ vault_developer_password }}"
    policies: ["developer"]
  - username: "jenkins"
    password: "{{ vault_jenkins_password }}"
    policies: ["ci-cd"]

# Database connections
vault_database_connections:
  - name: "postgres-db"
    plugin_name: "postgresql-database-plugin"
    connection_url: "postgresql://{{username}}:{{password}}@postgres:5432/app?sslmode=disable"
    allowed_roles: ["app-role"]
    username: "vault"
    password: "{{ vault_postgres_vault_password }}"
  - name: "mysql-db"
    plugin_name: "mysql-database-plugin"
    connection_url: "{{username}}:{{password}}@tcp(mysql:3306)/"
    allowed_roles: ["app-role"]
    username: "vault"
    password: "{{ vault_mysql_vault_password }}"

# Database roles
vault_database_roles:
  - name: "app-role"
    db_name: "postgres-db"
    creation_statements: |
      CREATE ROLE "{{name}}" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}';
      GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO "{{name}}";
    default_ttl: "1h"
    max_ttl: "24h"

# PKI configuration
vault_pki_config:
  root_ca:
    common_name: "Example Root CA"
    ttl: "8760h"
    key_bits: 4096
  intermediate_ca:
    common_name: "Example Intermediate CA"
    ttl: "4380h"
    key_bits: 2048
  roles:
    - name: "example-dot-com"
      allowed_domains: ["example.com"]
      allow_subdomains: true
      max_ttl: "720h"

# Monitoring and logging
vault_telemetry_enabled: true
vault_audit_enabled: true
vault_audit_devices:
  - type: "file"
    path: "file"
    options:
      file_path: "{{ vault_log_dir }}/audit.log"
  - type: "syslog"
    path: "syslog"
    options:
      facility: "AUTH"
      tag: "vault"

# Backup configuration
vault_backup_enabled: true
vault_backup_schedule: "0 2 * * *"
vault_backup_retention: 30
vault_backup_path: "/opt/backups/vault"

# Integration with other services
vault_jenkins_integration: true
vault_kubernetes_integration: true
vault_consul_integration: false
