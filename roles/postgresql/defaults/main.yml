---
# PostgreSQL role default variables

# PostgreSQL version and installation
postgresql_version: "15"
postgresql_package_state: present
postgresql_service_name: postgresql
postgresql_data_dir: "/var/lib/postgresql/{{ postgresql_version }}/main"
postgresql_config_dir: "/etc/postgresql/{{ postgresql_version }}/main"
postgresql_log_dir: /var/log/postgresql
postgresql_user: postgres
postgresql_group: postgres

# Network configuration
postgresql_listen_addresses: "*"
postgresql_port: 5432
postgresql_max_connections: 200
postgresql_superuser_reserved_connections: 3

# Authentication
postgresql_password_encryption: scram-sha-256
postgresql_ssl: true
postgresql_ssl_cert_file: server.crt
postgresql_ssl_key_file: server.key
postgresql_ssl_ca_file: root.crt

# Memory configuration
postgresql_shared_buffers: "256MB"
postgresql_effective_cache_size: "1GB"
postgresql_maintenance_work_mem: "64MB"
postgresql_checkpoint_completion_target: 0.9
postgresql_wal_buffers: "16MB"
postgresql_default_statistics_target: 100
postgresql_random_page_cost: 1.1
postgresql_effective_io_concurrency: 200
postgresql_work_mem: "4MB"
postgresql_min_wal_size: "1GB"
postgresql_max_wal_size: "4GB"

# Logging configuration
postgresql_log_destination: stderr
postgresql_logging_collector: true
postgresql_log_directory: pg_log
postgresql_log_filename: "postgresql-%Y-%m-%d_%H%M%S.log"
postgresql_log_file_mode: "0600"
postgresql_log_truncate_on_rotation: false
postgresql_log_rotation_age: "1d"
postgresql_log_rotation_size: "10MB"
postgresql_log_min_duration_statement: 1000
postgresql_log_checkpoints: true
postgresql_log_connections: false
postgresql_log_disconnections: false
postgresql_log_lock_waits: true
postgresql_log_statement: "none"
postgresql_log_line_prefix: "%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h "

# Replication configuration
postgresql_replication_enabled: false
postgresql_replication_role: master  # master or slave
postgresql_replication_user: replicator
postgresql_replication_password: "{{ vault_postgresql_replication_password }}"
postgresql_wal_level: replica
postgresql_max_wal_senders: 10
postgresql_wal_keep_size: "1GB"
postgresql_hot_standby: true
postgresql_hot_standby_feedback: false
postgresql_max_standby_streaming_delay: "30s"
postgresql_max_standby_archive_delay: "30s"

# Streaming replication
postgresql_primary_host: ""
postgresql_primary_port: 5432
postgresql_restore_command: ""
postgresql_archive_mode: false
postgresql_archive_command: ""

# Connection pooling with PgBouncer
postgresql_pgbouncer_enabled: false
postgresql_pgbouncer_port: 6432
postgresql_pgbouncer_pool_mode: session
postgresql_pgbouncer_max_client_conn: 100
postgresql_pgbouncer_default_pool_size: 20

# Backup configuration
postgresql_backup_enabled: true
postgresql_backup_user: backup
postgresql_backup_password: "{{ vault_postgresql_backup_password }}"
postgresql_backup_dir: /opt/postgresql-backups
postgresql_backup_schedule: "0 2 * * *"
postgresql_backup_retention_days: 7
postgresql_backup_compress: true
postgresql_backup_format: custom  # custom, plain, directory, tar

# Monitoring
postgresql_monitoring_enabled: true
postgresql_exporter_enabled: true
postgresql_exporter_port: 9187
postgresql_exporter_user: postgres_exporter
postgresql_exporter_password: "{{ vault_postgresql_exporter_password }}"

# Extensions
postgresql_extensions:
  - pg_stat_statements
  - pgcrypto
  - uuid-ossp
  - hstore
  - ltree
  - citext

# Databases to create
postgresql_databases:
  - name: example_db
    owner: example_user
    encoding: UTF8
    lc_collate: en_US.UTF-8
    lc_ctype: en_US.UTF-8
    template: template0

# Users to create
postgresql_users:
  - name: example_user
    password: "{{ vault_postgresql_example_password }}"
    role_attr_flags: CREATEDB,NOSUPERUSER
    db: example_db
    priv: ALL

# HBA (Host-Based Authentication) configuration
postgresql_hba_entries:
  - type: local
    database: all
    user: postgres
    auth_method: peer
  - type: local
    database: all
    user: all
    auth_method: peer
  - type: host
    database: all
    user: all
    address: "127.0.0.1/32"
    auth_method: md5
  - type: host
    database: all
    user: all
    address: "::1/128"
    auth_method: md5
  - type: host
    database: replication
    user: "{{ postgresql_replication_user }}"
    address: "0.0.0.0/0"
    auth_method: md5

# Performance tuning
postgresql_checkpoint_segments: 32
postgresql_checkpoint_timeout: "5min"
postgresql_autovacuum: true
postgresql_autovacuum_max_workers: 3
postgresql_autovacuum_naptime: "1min"
postgresql_vacuum_cost_delay: "0ms"
postgresql_vacuum_cost_page_hit: 1
postgresql_vacuum_cost_page_miss: 10
postgresql_vacuum_cost_page_dirty: 20
postgresql_vacuum_cost_limit: 200

# Security
postgresql_password_encryption: scram-sha-256
postgresql_row_security: true
postgresql_ssl_prefer_server_ciphers: true
postgresql_ssl_ciphers: "HIGH:MEDIUM:+3DES:!aNULL"
postgresql_ssl_protocols: "TLSv1.2,TLSv1.3"

# Custom configuration
postgresql_custom_config: {}
