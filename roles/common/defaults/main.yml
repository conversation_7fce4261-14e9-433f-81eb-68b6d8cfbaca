---
# Common role default variables

# System packages to install
common_packages:
  - curl
  - wget
  - vim
  - htop
  - git
  - unzip
  - tree
  - net-tools
  - rsync

# System users to create
common_users: []
# Example:
# common_users:
#   - name: deploy
#     groups: sudo
#     shell: /bin/bash
#     ssh_key: "{{ lookup('file', 'files/deploy_key.pub') }}"

# Timezone configuration
common_timezone: "UTC"

# NTP configuration
common_ntp_enabled: true
common_ntp_servers:
  - 0.pool.ntp.org
  - 1.pool.ntp.org
  - 2.pool.ntp.org
  - 3.pool.ntp.org

# SSH configuration
common_ssh_port: 22
common_ssh_permit_root_login: false
common_ssh_password_authentication: false
common_ssh_pubkey_authentication: true

# Firewall configuration
common_firewall_enabled: true
common_firewall_allowed_ports:
  - "{{ common_ssh_port }}/tcp"

# Log rotation
common_logrotate_enabled: true

# Swap configuration
common_swap_enabled: true
common_swap_size: "1G"

# System limits
common_limits_enabled: true
common_limits:
  - domain: "*"
    type: soft
    item: nofile
    value: 65536
  - domain: "*"
    type: hard
    item: nofile
    value: 65536
