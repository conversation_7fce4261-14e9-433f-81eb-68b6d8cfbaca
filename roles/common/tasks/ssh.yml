---
# SSH configuration tasks

- name: Configure SSH daemon
  lineinfile:
    path: /etc/ssh/sshd_config
    regexp: "{{ item.regexp }}"
    line: "{{ item.line }}"
    backup: yes
  with_items:
    - regexp: '^#?Port'
      line: "Port {{ common_ssh_port }}"
    - regexp: '^#?PermitRootLogin'
      line: "PermitRootLogin {{ 'yes' if common_ssh_permit_root_login else 'no' }}"
    - regexp: '^#?PasswordAuthentication'
      line: "PasswordAuthentication {{ 'yes' if common_ssh_password_authentication else 'no' }}"
    - regexp: '^#?PubkeyAuthentication'
      line: "PubkeyAuthentication {{ 'yes' if common_ssh_pubkey_authentication else 'no' }}"
    - regexp: '^#?ChallengeResponseAuthentication'
      line: "ChallengeResponseAuthentication no"
    - regexp: '^#?UsePAM'
      line: "UsePAM yes"
    - regexp: '^#?X11Forwarding'
      line: "X11Forwarding no"
    - regexp: '^#?PrintMotd'
      line: "PrintMotd no"
    - regexp: '^#?AcceptEnv'
      line: "AcceptEnv LANG LC_*"
    - regexp: '^#?Subsystem'
      line: "Subsystem sftp /usr/lib/openssh/sftp-server"
  notify: restart ssh
  tags: ssh_config

- name: Ensure SSH service is running and enabled
  service:
    name: "{{ ssh_service_name }}"
    state: started
    enabled: yes
  tags: ssh_service
