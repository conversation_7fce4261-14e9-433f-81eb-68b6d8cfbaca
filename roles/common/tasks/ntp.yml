---
# NTP configuration tasks

- name: Install NTP package
  package:
    name: "{{ ntp_package }}"
    state: present
  tags: ntp_install

- name: Configure NTP servers
  template:
    src: ntp.conf.j2
    dest: /etc/ntp.conf
    backup: yes
  notify: restart ntp
  when: ansible_os_family == "Debian"
  tags: ntp_config

- name: Configure chrony (RHEL/CentOS)
  template:
    src: chrony.conf.j2
    dest: /etc/chrony.conf
    backup: yes
  notify: restart chronyd
  when: ansible_os_family == "RedHat"
  tags: ntp_config

- name: Start and enable NTP service
  service:
    name: "{{ ntp_service }}"
    state: started
    enabled: yes
  tags: ntp_service

- name: Synchronize time immediately
  command: ntpdate -s {{ common_ntp_servers[0] }}
  ignore_errors: yes
  when: ansible_os_family == "Debian"
  tags: ntp_sync
