---
# Firewall configuration tasks

- name: Install UFW (Ubuntu/Debian)
  package:
    name: ufw
    state: present
  when: ansible_os_family == "Debian"
  tags: firewall_install

- name: Install firewalld (RHEL/CentOS)
  package:
    name: firewalld
    state: present
  when: ansible_os_family == "RedHat"
  tags: firewall_install

- name: Configure UFW default policies
  ufw:
    direction: "{{ item.direction }}"
    policy: "{{ item.policy }}"
  with_items:
    - { direction: 'incoming', policy: 'deny' }
    - { direction: 'outgoing', policy: 'allow' }
  when: ansible_os_family == "Debian"
  tags: firewall_policy

- name: Allow SSH through UFW
  ufw:
    rule: allow
    port: "{{ common_ssh_port }}"
    proto: tcp
  when: ansible_os_family == "Debian"
  tags: firewall_ssh

- name: Allow specified ports through UFW
  ufw:
    rule: allow
    port: "{{ item.split('/')[0] }}"
    proto: "{{ item.split('/')[1] }}"
  with_items: "{{ common_firewall_allowed_ports }}"
  when: ansible_os_family == "Debian"
  tags: firewall_ports

- name: Enable UFW
  ufw:
    state: enabled
  when: ansible_os_family == "Debian"
  tags: firewall_enable

- name: Start and enable firewalld
  service:
    name: firewalld
    state: started
    enabled: yes
  when: ansible_os_family == "RedHat"
  tags: firewall_service

- name: Configure firewalld zones
  firewalld:
    zone: public
    service: ssh
    permanent: yes
    state: enabled
    immediate: yes
  when: ansible_os_family == "RedHat"
  tags: firewall_zones
