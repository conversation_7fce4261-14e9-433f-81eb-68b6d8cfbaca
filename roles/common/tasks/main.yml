---
# Common role main tasks

- name: Include OS-specific variables
  include_vars: "{{ ansible_os_family }}.yml"
  tags: always

- name: Update package cache
  package:
    update_cache: yes
  tags: packages

- name: Install common packages
  package:
    name: "{{ common_packages }}"
    state: present
  tags: packages

- name: Set timezone
  timezone:
    name: "{{ common_timezone }}"
  notify: restart rsyslog
  tags: timezone

- name: Configure NTP
  include_tasks: ntp.yml
  when: common_ntp_enabled
  tags: ntp

- name: Create system users
  include_tasks: users.yml
  when: common_users | length > 0
  tags: users

- name: Configure SSH
  include_tasks: ssh.yml
  tags: ssh

- name: Configure firewall
  include_tasks: firewall.yml
  when: common_firewall_enabled
  tags: firewall

- name: Configure swap
  include_tasks: swap.yml
  when: common_swap_enabled
  tags: swap

- name: Configure system limits
  include_tasks: limits.yml
  when: common_limits_enabled
  tags: limits

- name: Configure log rotation
  include_tasks: logrotate.yml
  when: common_logrotate_enabled
  tags: logrotate
