---
# Log rotation configuration

- name: Install logrotate
  package:
    name: logrotate
    state: present
  tags: logrotate_install

- name: Configure logrotate for application logs
  template:
    src: logrotate.conf.j2
    dest: /etc/logrotate.d/applications
    mode: '0644'
  tags: logrotate_config

- name: Configure logrotate for system logs
  template:
    src: system-logrotate.conf.j2
    dest: /etc/logrotate.d/system-custom
    mode: '0644'
  tags: logrotate_config

- name: Test logrotate configuration
  command: logrotate -d /etc/logrotate.conf
  register: logrotate_test
  changed_when: false
  tags: logrotate_test

- name: Display logrotate test results
  debug:
    var: logrotate_test.stdout_lines
  tags: logrotate_test
