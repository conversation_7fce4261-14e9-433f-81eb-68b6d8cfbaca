---
# Swap configuration tasks

- name: Check current swap usage
  command: swapon --show
  register: swap_status
  changed_when: false
  failed_when: false
  tags: swap_check

- name: Create swap file
  command: fallocate -l {{ common_swap_size }} /swapfile
  args:
    creates: /swapfile
  when: common_swap_enabled and swap_status.stdout == ""
  tags: swap_create

- name: Set swap file permissions
  file:
    path: /swapfile
    mode: '0600'
  when: common_swap_enabled
  tags: swap_permissions

- name: Format swap file
  command: mkswap /swapfile
  when: common_swap_enabled and swap_status.stdout == ""
  tags: swap_format

- name: Enable swap file
  command: swapon /swapfile
  when: common_swap_enabled and swap_status.stdout == ""
  tags: swap_enable

- name: Add swap to fstab
  lineinfile:
    path: /etc/fstab
    line: '/swapfile none swap sw 0 0'
    state: present
  when: common_swap_enabled
  tags: swap_fstab

- name: Configure swappiness
  sysctl:
    name: vm.swappiness
    value: '10'
    state: present
    reload: yes
  when: common_swap_enabled
  tags: swap_tuning
