---
# User management tasks

- name: Create system users
  user:
    name: "{{ item.name }}"
    groups: "{{ item.groups | default([]) }}"
    shell: "{{ item.shell | default('/bin/bash') }}"
    create_home: yes
    state: present
  with_items: "{{ common_users }}"
  tags: create_users

- name: Set up SSH keys for users
  authorized_key:
    user: "{{ item.name }}"
    key: "{{ item.ssh_key }}"
    state: present
  with_items: "{{ common_users }}"
  when: item.ssh_key is defined
  tags: ssh_keys

- name: Configure sudo access
  lineinfile:
    path: /etc/sudoers.d/{{ item.name }}
    line: "{{ item.name }} ALL=(ALL) NOPASSWD:ALL"
    create: yes
    mode: '0440'
    validate: 'visudo -cf %s'
  with_items: "{{ common_users }}"
  when: item.sudo | default(false)
  tags: sudo_access
