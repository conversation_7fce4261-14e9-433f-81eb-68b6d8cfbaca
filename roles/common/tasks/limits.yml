---
# System limits configuration

- name: Configure system limits
  pam_limits:
    domain: "{{ item.domain }}"
    limit_type: "{{ item.type }}"
    limit_item: "{{ item.item }}"
    value: "{{ item.value }}"
  with_items: "{{ common_limits }}"
  tags: system_limits

- name: Configure systemd user limits
  lineinfile:
    path: /etc/systemd/user.conf
    regexp: '^#?DefaultLimitNOFILE='
    line: 'DefaultLimitNOFILE=65536'
    backup: yes
  tags: systemd_limits

- name: Configure systemd system limits
  lineinfile:
    path: /etc/systemd/system.conf
    regexp: '^#?DefaultLimitNOFILE='
    line: 'DefaultLimitNOFILE=65536'
    backup: yes
  tags: systemd_limits

- name: Reload systemd configuration
  systemd:
    daemon_reload: yes
  tags: systemd_limits
