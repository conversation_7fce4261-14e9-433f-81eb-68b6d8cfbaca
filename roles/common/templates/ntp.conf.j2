# NTP configuration file
# Managed by Ansible

# Use public servers from the pool.ntp.org project
{% for server in common_ntp_servers %}
server {{ server }} iburst
{% endfor %}

# Use Ubuntu's ntp server as a fallback
server ntp.ubuntu.com

# Access control configuration
restrict -4 default kod notrap nomodify nopeer noquery limited
restrict -6 default kod notrap nomodify nopeer noquery limited

# Local users may interrogate the ntp server more closely
restrict 127.0.0.1
restrict ::1

# Needed for adding pool entries
restrict source notrap nomodify noquery

# Clients from this (example!) subnet have unlimited access, but only if
# cryptographically authenticated.
#restrict ************* mask ************* notrust

# If you want to provide time to the local subnet, change the next line.
# (Again, the address is an example, and should be changed to match your subnet.)
#broadcast ***************

# If you want to listen to time broadcasts on your local subnet, de-comment the
# next lines. Please do this only if you trust everybody on the network!
#disable auth
#broadcastclient

# Drift file location
driftfile /var/lib/ntp/ntp.drift

# Leap seconds definition provided by tzdata
leapfile /usr/share/zoneinfo/leap-seconds.list

# Enable this if you want statistics to be logged.
#statsdir /var/log/ntpstats/

statistics loopstats peerstats clockstats
filegen loopstats file loopstats type day enable
filegen peerstats file peerstats type day enable
filegen clockstats file clockstats type day enable

# Enable kernel PLL/FLL clock discipline
tinker panic 0
