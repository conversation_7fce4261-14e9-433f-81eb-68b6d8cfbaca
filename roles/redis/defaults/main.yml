---
# Redis role default variables

# Redis version and installation
redis_version: "7.2"
redis_package_state: present
redis_service_name: redis-server
redis_user: redis
redis_group: redis
redis_config_file: /etc/redis/redis.conf
redis_data_dir: /var/lib/redis
redis_log_dir: /var/log/redis
redis_pid_file: /var/run/redis/redis-server.pid

# Network configuration
redis_bind: "0.0.0.0"
redis_port: 6379
redis_tcp_backlog: 511
redis_timeout: 0
redis_tcp_keepalive: 300
redis_protected_mode: false

# Security configuration
redis_requirepass: "{{ vault_redis_password }}"
redis_rename_commands: []
# Example:
# redis_rename_commands:
#   - from: FLUSHDB
#     to: FLUSHDB_MY_SECRET_NAME
#   - from: FLUSHALL
#     to: ""  # Disable command

# Memory configuration
redis_maxmemory: "1gb"
redis_maxmemory_policy: allkeys-lru
redis_maxmemory_samples: 5

# Persistence configuration
redis_save:
  - "900 1"    # Save if at least 1 key changed in 900 seconds
  - "300 10"   # Save if at least 10 keys changed in 300 seconds
  - "60 10000" # Save if at least 10000 keys changed in 60 seconds

redis_stop_writes_on_bgsave_error: true
redis_rdbcompression: true
redis_rdbchecksum: true
redis_dbfilename: dump.rdb
redis_dir: "{{ redis_data_dir }}"

# AOF (Append Only File) configuration
redis_appendonly: true
redis_appendfilename: appendonly.aof
redis_appendfsync: everysec
redis_no_appendfsync_on_rewrite: false
redis_auto_aof_rewrite_percentage: 100
redis_auto_aof_rewrite_min_size: 64mb
redis_aof_load_truncated: true
redis_aof_use_rdb_preamble: true

# Replication configuration
redis_replication_enabled: false
redis_replication_role: master  # master or slave
redis_master_host: ""
redis_master_port: 6379
redis_master_auth: ""
redis_slave_serve_stale_data: true
redis_slave_read_only: true
redis_repl_diskless_sync: false
redis_repl_diskless_sync_delay: 5
redis_repl_ping_slave_period: 10
redis_repl_timeout: 60
redis_repl_disable_tcp_nodelay: false
redis_repl_backlog_size: 1mb
redis_repl_backlog_ttl: 3600

# Sentinel configuration
redis_sentinel_enabled: false
redis_sentinel_port: 26379
redis_sentinel_master_name: mymaster
redis_sentinel_quorum: 2
redis_sentinel_down_after_milliseconds: 30000
redis_sentinel_parallel_syncs: 1
redis_sentinel_failover_timeout: 180000

# Cluster configuration
redis_cluster_enabled: false
redis_cluster_config_file: nodes-6379.conf
redis_cluster_node_timeout: 15000
redis_cluster_slave_validity_factor: 10
redis_cluster_migration_barrier: 1
redis_cluster_require_full_coverage: true

# Logging configuration
redis_loglevel: notice
redis_logfile: "{{ redis_log_dir }}/redis-server.log"
redis_syslog_enabled: false
redis_syslog_ident: redis
redis_syslog_facility: local0

# Performance tuning
redis_databases: 16
redis_hz: 10
redis_dynamic_hz: true
redis_aof_rewrite_incremental_fsync: true
redis_rdb_save_incremental_fsync: true
redis_latency_monitor_threshold: 100

# Slow log configuration
redis_slowlog_log_slower_than: 10000
redis_slowlog_max_len: 128

# Client configuration
redis_maxclients: 10000

# Lua scripting
redis_lua_time_limit: 5000

# Monitoring
redis_monitoring_enabled: true
redis_exporter_enabled: true
redis_exporter_port: 9121
redis_exporter_password: "{{ vault_redis_exporter_password }}"

# Backup configuration
redis_backup_enabled: true
redis_backup_dir: /opt/redis-backups
redis_backup_schedule: "0 2 * * *"
redis_backup_retention_days: 7
redis_backup_compress: true

# SSL/TLS configuration
redis_tls_enabled: false
redis_tls_port: 6380
redis_tls_cert_file: /etc/redis/tls/redis.crt
redis_tls_key_file: /etc/redis/tls/redis.key
redis_tls_ca_cert_file: /etc/redis/tls/ca.crt
redis_tls_protocols: "TLSv1.2 TLSv1.3"
redis_tls_ciphers: "HIGH:!aNULL:!MD5"
redis_tls_ciphersuites: "TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256"

# Modules
redis_modules: []
# Example:
# redis_modules:
#   - /usr/lib/redis/modules/redisearch.so
#   - /usr/lib/redis/modules/redisjson.so

# Custom configuration
redis_custom_config: {}

# Firewall configuration
redis_firewall_enabled: true
redis_firewall_allowed_hosts:
  - "10.0.0.0/8"
  - "**********/12"
  - "***********/16"
