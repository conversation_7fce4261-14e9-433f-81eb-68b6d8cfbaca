---
# Elasticsearch role default variables

# Elasticsearch version and installation
elasticsearch_version: "8.10.0"
elasticsearch_package_state: present
elasticsearch_service_name: elasticsearch
elasticsearch_user: elasticsearch
elasticsearch_group: elasticsearch
elasticsearch_home: /usr/share/elasticsearch
elasticsearch_config_dir: /etc/elasticsearch
elasticsearch_data_dir: /var/lib/elasticsearch
elasticsearch_log_dir: /var/log/elasticsearch
elasticsearch_pid_dir: /var/run/elasticsearch

# Cluster configuration
elasticsearch_cluster_name: "elasticsearch-cluster"
elasticsearch_node_name: "{{ inventory_hostname }}"
elasticsearch_node_roles:
  - master
  - data
  - ingest
elasticsearch_network_host: "0.0.0.0"
elasticsearch_http_port: 9200
elasticsearch_transport_port: 9300

# Discovery and cluster formation
elasticsearch_discovery_seed_hosts: []
elasticsearch_cluster_initial_master_nodes: []
elasticsearch_discovery_type: zen

# Memory configuration
elasticsearch_heap_size: "1g"
elasticsearch_memory_lock: true

# Security configuration
elasticsearch_security_enabled: true
elasticsearch_security_transport_ssl_enabled: true
elasticsearch_security_http_ssl_enabled: true
elasticsearch_security_autoconfiguration_enabled: false

# SSL/TLS configuration
elasticsearch_ssl_certificate_authorities: []
elasticsearch_ssl_certificate: /etc/elasticsearch/certs/elasticsearch.crt
elasticsearch_ssl_key: /etc/elasticsearch/certs/elasticsearch.key
elasticsearch_ssl_verification_mode: certificate

# Authentication
elasticsearch_users:
  - username: elastic
    password: "{{ vault_elasticsearch_elastic_password }}"
    roles: ["superuser"]
  - username: kibana_system
    password: "{{ vault_elasticsearch_kibana_password }}"
    roles: ["kibana_system"]
  - username: logstash_system
    password: "{{ vault_elasticsearch_logstash_password }}"
    roles: ["logstash_system"]

# Index configuration
elasticsearch_action_destructive_requires_name: true
elasticsearch_index_number_of_shards: 1
elasticsearch_index_number_of_replicas: 1
elasticsearch_index_refresh_interval: "1s"

# Performance tuning
elasticsearch_indices_memory_index_buffer_size: "10%"
elasticsearch_indices_memory_min_index_buffer_size: "48mb"
elasticsearch_indices_queries_cache_size: "10%"
elasticsearch_indices_fielddata_cache_size: "20%"
elasticsearch_thread_pool_write_queue_size: 200
elasticsearch_thread_pool_search_queue_size: 1000

# Monitoring
elasticsearch_monitoring_enabled: true
elasticsearch_monitoring_collection_enabled: true
elasticsearch_exporter_enabled: true
elasticsearch_exporter_port: 9114

# Backup configuration
elasticsearch_backup_enabled: true
elasticsearch_backup_repository_type: fs  # fs, s3, gcs, azure
elasticsearch_backup_repository_location: /opt/elasticsearch-backups
elasticsearch_backup_schedule: "0 2 * * *"
elasticsearch_backup_retention: "30d"

# S3 backup configuration (if using S3)
elasticsearch_s3_backup_bucket: ""
elasticsearch_s3_backup_region: "us-east-1"
elasticsearch_s3_backup_access_key: "{{ vault_elasticsearch_s3_access_key }}"
elasticsearch_s3_backup_secret_key: "{{ vault_elasticsearch_s3_secret_key }}"

# Logging configuration
elasticsearch_log_level: INFO
elasticsearch_slow_log_threshold_query_warn: "10s"
elasticsearch_slow_log_threshold_query_info: "5s"
elasticsearch_slow_log_threshold_query_debug: "2s"
elasticsearch_slow_log_threshold_query_trace: "500ms"
elasticsearch_slow_log_threshold_fetch_warn: "1s"
elasticsearch_slow_log_threshold_fetch_info: "800ms"
elasticsearch_slow_log_threshold_fetch_debug: "500ms"
elasticsearch_slow_log_threshold_fetch_trace: "200ms"

# Index lifecycle management
elasticsearch_ilm_enabled: true
elasticsearch_ilm_policies:
  - name: "logs-policy"
    policy:
      phases:
        hot:
          actions:
            rollover:
              max_size: "10gb"
              max_age: "7d"
        warm:
          min_age: "7d"
          actions:
            allocate:
              number_of_replicas: 0
        cold:
          min_age: "30d"
          actions:
            allocate:
              number_of_replicas: 0
        delete:
          min_age: "90d"

# Index templates
elasticsearch_index_templates:
  - name: "logs-template"
    index_patterns: ["logs-*"]
    settings:
      number_of_shards: 1
      number_of_replicas: 1
      index.lifecycle.name: "logs-policy"
      index.lifecycle.rollover_alias: "logs"

# Plugins
elasticsearch_plugins:
  - repository-s3
  - ingest-attachment
  - analysis-icu

# Custom configuration
elasticsearch_custom_config: {}

# JVM options
elasticsearch_jvm_options:
  - "-Xms{{ elasticsearch_heap_size }}"
  - "-Xmx{{ elasticsearch_heap_size }}"
  - "-XX:+UseG1GC"
  - "-XX:G1HeapRegionSize=32m"
  - "-XX:+UseG1OldGCMixedGCCount=16"
  - "-XX:+UseStringDeduplication"
  - "-Djava.io.tmpdir=${ES_TMPDIR}"
  - "-XX:+HeapDumpOnOutOfMemoryError"
  - "-XX:HeapDumpPath={{ elasticsearch_data_dir }}"
  - "-XX:ErrorFile={{ elasticsearch_log_dir }}/hs_err_pid%p.log"

# Firewall configuration
elasticsearch_firewall_enabled: true
elasticsearch_firewall_allowed_hosts:
  - "10.0.0.0/8"
  - "**********/12"
  - "***********/16"
