user {{ nginx_user }};
worker_processes {{ nginx_worker_processes }};
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
    worker_connections {{ nginx_worker_connections }};
    use epoll;
    multi_accept on;
}

http {
    # Basic Settings
    sendfile {{ nginx_sendfile }};
    tcp_nopush {{ nginx_tcp_nopush }};
    tcp_nodelay {{ nginx_tcp_nodelay }};
    keepalive_timeout {{ nginx_keepalive_timeout }};
    types_hash_max_size 2048;
    client_max_body_size {{ nginx_client_max_body_size }};
    server_tokens off;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # SSL Settings
{% if nginx_ssl_enabled %}
    ssl_protocols {{ nginx_ssl_protocols }};
    ssl_prefer_server_ciphers on;
    ssl_ciphers {{ nginx_ssl_ciphers }};
{% endif %}

    # Logging Settings
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log {{ nginx_access_log }} main;
    error_log {{ nginx_error_log }};

    # Gzip Settings
    gzip {{ nginx_gzip }};
    gzip_vary {{ nginx_gzip_vary }};
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types {{ nginx_gzip_types }};

    # Security Headers
{% for header in nginx_security_headers %}
    add_header {{ header }} always;
{% endfor %}

    # Virtual Host Configs
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
