---
# Webserver role default variables

# Nginx configuration
nginx_user: www-data
nginx_worker_processes: auto
nginx_worker_connections: 1024
nginx_keepalive_timeout: 65
nginx_client_max_body_size: 64m

# SSL configuration
nginx_ssl_enabled: false
nginx_ssl_certificate: /etc/ssl/certs/nginx.crt
nginx_ssl_certificate_key: /etc/ssl/private/nginx.key
nginx_ssl_protocols: "TLSv1.2 TLSv1.3"
nginx_ssl_ciphers: "ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384"

# Virtual hosts
nginx_vhosts: []
# Example:
# nginx_vhosts:
#   - server_name: example.com
#     document_root: /var/www/example.com
#     index: index.html index.php
#     ssl: false

# Default site configuration
nginx_remove_default_vhost: true
nginx_default_vhost_enabled: false

# Log configuration
nginx_access_log: /var/log/nginx/access.log
nginx_error_log: /var/log/nginx/error.log

# Performance tuning
nginx_sendfile: "on"
nginx_tcp_nopush: "on"
nginx_tcp_nodelay: "on"
nginx_gzip: "on"
nginx_gzip_vary: "on"
nginx_gzip_types: "text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript"

# Security headers
nginx_security_headers:
  - "X-Frame-Options DENY"
  - "X-Content-Type-Options nosniff"
  - "X-XSS-Protection 1; mode=block"
  - "Referrer-Policy strict-origin-when-cross-origin"
