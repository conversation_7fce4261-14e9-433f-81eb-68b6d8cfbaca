---
# Webserver role main tasks

- name: Install Nginx
  package:
    name: nginx
    state: present
  tags: nginx_install

- name: Create web directories
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ nginx_user }}"
    group: "{{ nginx_user }}"
    mode: '0755'
  with_items:
    - /var/www
    - /var/log/nginx
  tags: nginx_directories

- name: Configure Nginx main configuration
  template:
    src: nginx.conf.j2
    dest: /etc/nginx/nginx.conf
    backup: yes
  notify: restart nginx
  tags: nginx_config

- name: Remove default Nginx site
  file:
    path: "{{ item }}"
    state: absent
  with_items:
    - /etc/nginx/sites-enabled/default
    - /etc/nginx/sites-available/default
  when: nginx_remove_default_vhost
  notify: restart nginx
  tags: nginx_default_site

- name: Create virtual host configurations
  template:
    src: vhost.conf.j2
    dest: "/etc/nginx/sites-available/{{ item.server_name }}"
  with_items: "{{ nginx_vhosts }}"
  notify: restart nginx
  tags: nginx_vhosts

- name: Enable virtual hosts
  file:
    src: "/etc/nginx/sites-available/{{ item.server_name }}"
    dest: "/etc/nginx/sites-enabled/{{ item.server_name }}"
    state: link
  with_items: "{{ nginx_vhosts }}"
  notify: restart nginx
  tags: nginx_vhosts

- name: Create document roots
  file:
    path: "{{ item.document_root }}"
    state: directory
    owner: "{{ nginx_user }}"
    group: "{{ nginx_user }}"
    mode: '0755'
  with_items: "{{ nginx_vhosts }}"
  tags: nginx_document_roots

- name: Start and enable Nginx service
  service:
    name: nginx
    state: started
    enabled: yes
  tags: nginx_service

- name: Configure firewall for HTTP/HTTPS
  ufw:
    rule: allow
    port: "{{ item }}"
    proto: tcp
  with_items:
    - '80'
    - '443'
  when: ansible_os_family == "Debian"
  tags: nginx_firewall
