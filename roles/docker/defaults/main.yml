---
# Docker role default variables

# Docker installation
docker_edition: ce
docker_package_state: present
docker_service_state: started
docker_service_enabled: true
docker_restart_handler_state: restarted

# Docker daemon configuration
docker_daemon_options:
  storage-driver: overlay2
  log-driver: json-file
  log-opts:
    max-size: "10m"
    max-file: "3"
  live-restore: true
  userland-proxy: false
  experimental: false

# Docker Compose
docker_compose_version: "2.20.2"
docker_compose_install: true
docker_compose_path: /usr/local/bin/docker-compose

# Docker users (users to add to docker group)
docker_users:
  - "{{ ansible_user }}"
  - jenkins

# Docker registry configuration
docker_registry_enabled: false
docker_registry_url: "registry.example.com"
docker_registry_username: ""
docker_registry_password: "{{ vault_docker_registry_password | default('') }}"

# Docker networks
docker_networks:
  - name: app-network
    driver: bridge
    ipam_config:
      - subnet: "**********/16"

# Docker volumes
docker_volumes:
  - name: jenkins-data
  - name: app-data
  - name: db-data

# Container cleanup
docker_cleanup_enabled: true
docker_cleanup_schedule: "0 3 * * 0"  # Weekly on Sunday at 3 AM

# Security settings
docker_enable_audit: true
docker_tls_enabled: false
docker_content_trust: false

# Resource limits
docker_default_ulimits:
  - name: nofile
    soft: 65536
    hard: 65536
  - name: memlock
    soft: -1
    hard: -1

# Logging
docker_log_driver: json-file
docker_log_max_size: "10m"
docker_log_max_file: "3"
