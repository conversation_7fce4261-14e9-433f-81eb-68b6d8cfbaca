{
  "storage-driver": "{{ docker_daemon_options['storage-driver'] | default('overlay2') }}",
  "log-driver": "{{ docker_daemon_options['log-driver'] | default('json-file') }}",
  "log-opts": {{ docker_daemon_options['log-opts'] | default({'max-size': '10m', 'max-file': '3'}) | to_json }},
  "live-restore": {{ docker_daemon_options['live-restore'] | default(true) | lower }},
  "userland-proxy": {{ docker_daemon_options['userland-proxy'] | default(false) | lower }},
  "experimental": {{ docker_daemon_options['experimental'] | default(false) | lower }},
  "metrics-addr": "0.0.0.0:9323",
  "experimental": false,
  "default-ulimits": {
{% for ulimit in docker_default_ulimits %}
    "{{ ulimit.name }}": {
      "Name": "{{ ulimit.name }}",
      "Soft": {{ ulimit.soft }},
      "Hard": {{ ulimit.hard }}
    }{% if not loop.last %},{% endif %}
{% endfor %}
  }{% if docker_registry_enabled %},
  "insecure-registries": ["{{ docker_registry_url }}"]{% endif %}
}
