---
# Docker daemon configuration

- name: Create Docker daemon configuration
  template:
    src: daemon.json.j2
    dest: /etc/docker/daemon.json
    mode: '0644'
  notify: restart docker
  tags: docker_config

- name: Create Docker systemd directory
  file:
    path: /etc/systemd/system/docker.service.d
    state: directory
    mode: '0755'
  tags: docker_systemd

- name: Configure Docker systemd service
  template:
    src: docker.service.j2
    dest: /etc/systemd/system/docker.service.d/override.conf
    mode: '0644'
  notify:
    - reload systemd
    - restart docker
  tags: docker_systemd

- name: Configure Docker log rotation
  template:
    src: docker-logrotate.j2
    dest: /etc/logrotate.d/docker
    mode: '0644'
  tags: docker_logrotate
