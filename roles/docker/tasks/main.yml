---
# Docker role main tasks

- name: Include OS-specific variables
  include_vars: "{{ ansible_os_family }}.yml"
  tags: always

- name: Install Docker prerequisites
  include_tasks: prerequisites.yml
  tags: prerequisites

- name: Install Docker
  include_tasks: install.yml
  tags: install

- name: Configure Docker daemon
  include_tasks: configure.yml
  tags: configure

- name: Install Docker Compose
  include_tasks: compose.yml
  when: docker_compose_install
  tags: compose

- name: Configure Docker users
  include_tasks: users.yml
  tags: users

- name: Create Docker networks
  include_tasks: networks.yml
  tags: networks

- name: Create Docker volumes
  include_tasks: volumes.yml
  tags: volumes

- name: Configure Docker registry
  include_tasks: registry.yml
  when: docker_registry_enabled
  tags: registry

- name: Configure Docker cleanup
  include_tasks: cleanup.yml
  when: docker_cleanup_enabled
  tags: cleanup

- name: Start and enable Docker service
  service:
    name: docker
    state: "{{ docker_service_state }}"
    enabled: "{{ docker_service_enabled }}"
  tags: service

- name: Verify Docker installation
  command: docker --version
  register: docker_version
  changed_when: false
  tags: verify

- name: Display Docker version
  debug:
    var: docker_version.stdout
  tags: verify
