---
# Docker prerequisites

- name: Update package cache
  package:
    update_cache: yes
  tags: docker_prereq

- name: Install Docker prerequisites (Debian/Ubuntu)
  package:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - gnupg
      - lsb-release
      - software-properties-common
    state: present
  when: ansible_os_family == "Debian"
  tags: docker_prereq

- name: Install Docker prerequisites (RHEL/CentOS)
  package:
    name:
      - yum-utils
      - device-mapper-persistent-data
      - lvm2
    state: present
  when: ansible_os_family == "RedHat"
  tags: docker_prereq

- name: Create Docker group
  group:
    name: docker
    state: present
  tags: docker_group
