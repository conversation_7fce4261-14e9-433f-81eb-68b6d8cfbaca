---
# Docker Compose installation

- name: Check if Docker Compose is installed
  command: docker-compose --version
  register: compose_installed
  changed_when: false
  failed_when: false
  tags: docker_compose

- name: Download Docker Compose
  get_url:
    url: "https://github.com/docker/compose/releases/download/v{{ docker_compose_version }}/docker-compose-Linux-x86_64"
    dest: "{{ docker_compose_path }}"
    mode: '0755'
  when: compose_installed.rc != 0
  tags: docker_compose

- name: Create Docker Compose symlink
  file:
    src: "{{ docker_compose_path }}"
    dest: /usr/bin/docker-compose
    state: link
  tags: docker_compose

- name: Verify Docker Compose installation
  command: docker-compose --version
  register: compose_version
  changed_when: false
  tags: docker_compose

- name: Display Docker Compose version
  debug:
    var: compose_version.stdout
  tags: docker_compose
