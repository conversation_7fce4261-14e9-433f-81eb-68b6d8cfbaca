---
# Docker cleanup configuration

- name: Create Docker cleanup script
  template:
    src: docker-cleanup.sh.j2
    dest: /usr/local/bin/docker-cleanup.sh
    mode: '0755'
  tags: docker_cleanup

- name: Schedule Docker cleanup
  cron:
    name: "Docker cleanup"
    minute: "0"
    hour: "3"
    weekday: "0"
    job: "/usr/local/bin/docker-cleanup.sh"
  when: docker_cleanup_schedule != ""
  tags: docker_cleanup
