---
# Docker registry configuration

- name: Login to Docker registry
  docker_login:
    registry_url: "{{ docker_registry_url }}"
    username: "{{ docker_registry_username }}"
    password: "{{ docker_registry_password }}"
    reauthorize: yes
  when: docker_registry_username != ""
  tags: docker_registry

- name: Create Docker registry configuration
  template:
    src: docker-registry-config.json.j2
    dest: /etc/docker/daemon.json
    mode: '0644'
  notify: restart docker
  tags: docker_registry
