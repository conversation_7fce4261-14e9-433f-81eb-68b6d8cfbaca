---
# Docker installation tasks

- name: Add Docker GPG key (Debian/Ubuntu)
  apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg
    state: present
  when: ansible_os_family == "Debian"
  tags: repository

- name: Add Docker repository (Debian/Ubuntu)
  apt_repository:
    repo: "deb [arch=amd64] https://download.docker.com/linux/ubuntu {{ ansible_distribution_release }} stable"
    state: present
    update_cache: yes
  when: ansible_os_family == "Debian"
  tags: repository

- name: Add Docker repository (RedHat/CentOS)
  yum_repository:
    name: docker-ce-stable
    description: Docker CE Stable - $basearch
    baseurl: https://download.docker.com/linux/centos/7/$basearch/stable
    gpgcheck: yes
    gpgkey: https://download.docker.com/linux/centos/gpg
    enabled: yes
  when: ansible_os_family == "RedHat"
  tags: repository

- name: Install Docker CE
  package:
    name: "docker-{{ docker_edition }}"
    state: "{{ docker_package_state }}"
  notify: restart docker
  tags: package

- name: Install Docker CLI
  package:
    name: "docker-{{ docker_edition }}-cli"
    state: "{{ docker_package_state }}"
  tags: package

- name: Install containerd
  package:
    name: containerd.io
    state: "{{ docker_package_state }}"
  tags: package

- name: Create Docker systemd directory
  file:
    path: /etc/systemd/system/docker.service.d
    state: directory
    mode: '0755'
  tags: systemd

- name: Create Docker configuration directory
  file:
    path: /etc/docker
    state: directory
    mode: '0755'
  tags: config
