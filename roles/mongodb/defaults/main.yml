---
# MongoDB role default variables

# MongoDB version and installation
mongodb_version: "7.0"
mongodb_package_state: present
mongodb_service_name: mongod
mongodb_user: mongodb
mongodb_group: mongodb
mongodb_data_dir: /var/lib/mongodb
mongodb_log_dir: /var/log/mongodb
mongodb_config_file: /etc/mongod.conf

# Network configuration
mongodb_bind_ip: "0.0.0.0"
mongodb_port: 27017
mongodb_max_connections: 1000

# Storage configuration
mongodb_storage_engine: wiredTiger
mongodb_storage_dbpath: "{{ mongodb_data_dir }}"
mongodb_storage_journal_enabled: true
mongodb_storage_directoryperdb: false
mongodb_storage_syncperiodms: 60

# WiredTiger configuration
mongodb_wiredtiger_cache_size_gb: 1
mongodb_wiredtiger_directory_for_indexes: false
mongodb_wiredtiger_collection_block_compressor: snappy
mongodb_wiredtiger_index_prefix_compression: true

# Replication configuration
mongodb_replication_enabled: false
mongodb_replica_set_name: "rs0"
mongodb_replica_set_members: []
# Example:
# mongodb_replica_set_members:
#   - host: "mongo1.example.com:27017"
#     priority: 1
#   - host: "mongo2.example.com:27017"
#     priority: 0.5
#   - host: "mongo3.example.com:27017"
#     priority: 0.5
#     arbiterOnly: true

# Sharding configuration
mongodb_sharding_enabled: false
mongodb_shard_role: shard  # shard, config, mongos
mongodb_config_servers: []
mongodb_shard_servers: []

# Security configuration
mongodb_auth_enabled: true
mongodb_root_admin_name: admin
mongodb_root_admin_password: "{{ vault_mongodb_root_password }}"
mongodb_keyfile_content: "{{ vault_mongodb_keyfile }}"
mongodb_ssl_enabled: false
mongodb_ssl_mode: requireSSL
mongodb_ssl_pem_key_file: /etc/ssl/mongodb.pem
mongodb_ssl_ca_file: /etc/ssl/mongodb-ca.crt

# Logging configuration
mongodb_log_destination: file
mongodb_log_path: "{{ mongodb_log_dir }}/mongod.log"
mongodb_log_append: true
mongodb_log_rotation: rename
mongodb_log_verbosity: 0
mongodb_log_quiet: false

# Profiling and monitoring
mongodb_profiling_enabled: false
mongodb_profiling_slow_op_threshold_ms: 100
mongodb_monitoring_enabled: true
mongodb_exporter_enabled: true
mongodb_exporter_port: 9216
mongodb_exporter_user: mongodb_exporter
mongodb_exporter_password: "{{ vault_mongodb_exporter_password }}"

# Backup configuration
mongodb_backup_enabled: true
mongodb_backup_user: backup
mongodb_backup_password: "{{ vault_mongodb_backup_password }}"
mongodb_backup_dir: /opt/mongodb-backups
mongodb_backup_schedule: "0 2 * * *"
mongodb_backup_retention_days: 7
mongodb_backup_compress: true

# Performance tuning
mongodb_operation_profiling_slow_op_threshold_ms: 100
mongodb_operation_profiling_mode: "off"  # off, slowOp, all
mongodb_net_max_incoming_connections: 65536
mongodb_net_max_connections: 1000000

# Databases to create
mongodb_databases:
  - name: example_db

# Users to create
mongodb_users:
  - database: admin
    name: "{{ mongodb_root_admin_name }}"
    password: "{{ mongodb_root_admin_password }}"
    roles: root
  - database: example_db
    name: example_user
    password: "{{ vault_mongodb_example_password }}"
    roles: readWrite

# Indexes to create
mongodb_indexes: []
# Example:
# mongodb_indexes:
#   - database: example_db
#     collection: users
#     keys:
#       email: 1
#     options:
#       unique: true

# Custom configuration
mongodb_custom_config: {}

# Firewall configuration
mongodb_firewall_enabled: true
mongodb_firewall_allowed_hosts:
  - "10.0.0.0/8"
  - "**********/12"
  - "***********/16"
