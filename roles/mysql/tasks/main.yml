---
# MySQL role main tasks

- name: Include OS-specific variables
  include_vars: "{{ ansible_os_family }}.yml"
  tags: always

- name: Install MySQL packages
  include_tasks: install.yml
  tags: mysql_install

- name: Configure MySQL
  include_tasks: configure.yml
  tags: mysql_configure

- name: Secure MySQL installation
  include_tasks: secure.yml
  tags: mysql_secure

- name: Setup MySQL replication
  include_tasks: replication.yml
  when: mysql_replication_enabled
  tags: mysql_replication

- name: Setup Galera cluster
  include_tasks: galera.yml
  when: mysql_galera_enabled
  tags: mysql_galera

- name: Configure SSL
  include_tasks: ssl.yml
  when: mysql_ssl_enabled
  tags: mysql_ssl

- name: Create databases
  include_tasks: databases.yml
  tags: mysql_databases

- name: Create users
  include_tasks: users.yml
  tags: mysql_users

- name: Setup backup
  include_tasks: backup.yml
  when: mysql_backup_enabled
  tags: mysql_backup

- name: Setup monitoring
  include_tasks: monitoring.yml
  when: mysql_monitoring_enabled
  tags: mysql_monitoring

- name: Configure firewall
  include_tasks: firewall.yml
  when: mysql_firewall_enabled
  tags: mysql_firewall

- name: Performance tuning
  include_tasks: tuning.yml
  tags: mysql_tuning

- name: Start and enable MySQL service
  service:
    name: "{{ mysql_service_name }}"
    state: started
    enabled: yes
  tags: mysql_service
