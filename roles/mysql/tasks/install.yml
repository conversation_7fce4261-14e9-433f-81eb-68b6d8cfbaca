---
# MySQL installation tasks

- name: Add MySQL APT repository key
  apt_key:
    url: https://repo.mysql.com/RPM-GPG-KEY-mysql-2022
    state: present
  when: ansible_os_family == "Debian"
  tags: mysql_repo

- name: Add MySQL APT repository
  apt_repository:
    repo: "deb http://repo.mysql.com/apt/ubuntu/ {{ ansible_distribution_release }} mysql-{{ mysql_version }}"
    state: present
    update_cache: yes
  when: ansible_os_family == "Debian"
  tags: mysql_repo

- name: Install MySQL server
  package:
    name: "{{ mysql_packages }}"
    state: "{{ mysql_package_state }}"
  tags: mysql_packages

- name: Install Python MySQL dependencies
  package:
    name: "{{ mysql_python_packages }}"
    state: present
  tags: mysql_python

- name: Create MySQL directories
  file:
    path: "{{ item }}"
    state: directory
    owner: mysql
    group: mysql
    mode: '0755'
  with_items:
    - "{{ mysql_data_dir }}"
    - "{{ mysql_log_dir }}"
    - "{{ mysql_backup_dir }}"
    - /etc/mysql/ssl
    - /var/lib/mysql-files
  tags: mysql_directories

- name: Set MySQL data directory permissions
  file:
    path: "{{ mysql_data_dir }}"
    owner: mysql
    group: mysql
    mode: '0700'
    recurse: yes
  tags: mysql_permissions
