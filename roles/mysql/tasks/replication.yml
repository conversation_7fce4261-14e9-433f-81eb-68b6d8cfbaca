---
# MySQL replication setup

- name: Create replication user on master
  mysql_user:
    name: "{{ mysql_replication_user }}"
    password: "{{ mysql_replication_password }}"
    priv: "*.*:REPLICATION SLAVE"
    host: "%"
    state: present
    login_user: root
    login_password: "{{ mysql_root_password }}"
  when: mysql_replication_role == "master"
  tags: replication_user

- name: Get master status
  mysql_replication:
    mode: getmaster
    login_user: root
    login_password: "{{ mysql_root_password }}"
  register: master_status
  when: mysql_replication_role == "master"
  tags: master_status

- name: Save master status to file
  copy:
    content: |
      MASTER_LOG_FILE='{{ master_status.File }}'
      MASTER_LOG_POS={{ master_status.Position }}
    dest: /tmp/master_status.txt
    mode: '0600'
  when: mysql_replication_role == "master"
  tags: master_status

- name: Fetch master status
  fetch:
    src: /tmp/master_status.txt
    dest: /tmp/master_status.txt
    flat: yes
  when: mysql_replication_role == "master"
  tags: master_status

- name: Read master status
  slurp:
    src: /tmp/master_status.txt
  register: master_status_content
  delegate_to: localhost
  when: mysql_replication_role == "slave"
  tags: slave_setup

- name: Parse master status
  set_fact:
    master_log_file: "{{ master_status_content.content | b64decode | regex_search(\"MASTER_LOG_FILE='([^']+)'\", '\\1') | first }}"
    master_log_pos: "{{ master_status_content.content | b64decode | regex_search('MASTER_LOG_POS=([0-9]+)', '\\1') | first }}"
  when: mysql_replication_role == "slave"
  tags: slave_setup

- name: Stop slave
  mysql_replication:
    mode: stopslave
    login_user: root
    login_password: "{{ mysql_root_password }}"
  when: mysql_replication_role == "slave"
  ignore_errors: yes
  tags: slave_setup

- name: Configure slave
  mysql_replication:
    mode: changemaster
    master_host: "{{ mysql_replication_master_host }}"
    master_port: "{{ mysql_replication_master_port }}"
    master_user: "{{ mysql_replication_user }}"
    master_password: "{{ mysql_replication_password }}"
    master_log_file: "{{ master_log_file }}"
    master_log_pos: "{{ master_log_pos }}"
    login_user: root
    login_password: "{{ mysql_root_password }}"
  when: mysql_replication_role == "slave"
  tags: slave_setup

- name: Start slave
  mysql_replication:
    mode: startslave
    login_user: root
    login_password: "{{ mysql_root_password }}"
  when: mysql_replication_role == "slave"
  tags: slave_setup

- name: Check slave status
  mysql_replication:
    mode: getslave
    login_user: root
    login_password: "{{ mysql_root_password }}"
  register: slave_status
  when: mysql_replication_role == "slave"
  tags: slave_status

- name: Display slave status
  debug:
    msg: |
      Slave Status:
      - Slave_IO_Running: {{ slave_status.Slave_IO_Running }}
      - Slave_SQL_Running: {{ slave_status.Slave_SQL_Running }}
      - Seconds_Behind_Master: {{ slave_status.Seconds_Behind_Master }}
  when: mysql_replication_role == "slave"
  tags: slave_status
