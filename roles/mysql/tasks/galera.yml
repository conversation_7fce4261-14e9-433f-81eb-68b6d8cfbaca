---
# MySQL Galera cluster setup

- name: Install Galera packages
  package:
    name: "{{ galera_packages }}"
    state: present
  tags: galera_install

- name: Configure Galera cluster
  template:
    src: galera.cnf.j2
    dest: /etc/mysql/conf.d/galera.cnf
    backup: yes
  notify: restart mysql
  tags: galera_config

- name: Stop MySQL service for cluster initialization
  service:
    name: "{{ mysql_service_name }}"
    state: stopped
  when: mysql_galera_bootstrap | default(false)
  tags: galera_bootstrap

- name: Bootstrap Galera cluster
  command: galera_new_cluster
  when: mysql_galera_bootstrap | default(false)
  tags: galera_bootstrap

- name: Start MySQL service on other nodes
  service:
    name: "{{ mysql_service_name }}"
    state: started
  when: not (mysql_galera_bootstrap | default(false))
  tags: galera_start

- name: Wait for Galera cluster to be ready
  wait_for:
    port: "{{ mysql_port }}"
    host: "{{ ansible_default_ipv4.address }}"
    delay: 10
    timeout: 300
  tags: galera_ready

- name: Check Galera cluster status
  mysql_query:
    login_user: root
    login_password: "{{ mysql_root_password }}"
    query: "SHOW STATUS LIKE 'wsrep_%'"
  register: galera_status
  tags: galera_status

- name: Display Galera cluster information
  debug:
    msg: |
      Galera Cluster Status:
      - Cluster Size: {{ galera_status.query_result | selectattr('Variable_name', 'equalto', 'wsrep_cluster_size') | map(attribute='Value') | first }}
      - Node State: {{ galera_status.query_result | selectattr('Variable_name', 'equalto', 'wsrep_local_state_comment') | map(attribute='Value') | first }}
      - Ready: {{ galera_status.query_result | selectattr('Variable_name', 'equalto', 'wsrep_ready') | map(attribute='Value') | first }}
  tags: galera_status
