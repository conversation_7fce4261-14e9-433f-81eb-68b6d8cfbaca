---
# MySQL role default variables

# MySQL version and installation
mysql_version: "8.0"
mysql_package_state: present
mysql_service_name: mysql
mysql_config_file: /etc/mysql/mysql.conf.d/mysqld.cnf
mysql_data_dir: /var/lib/mysql
mysql_log_dir: /var/log/mysql
mysql_tmp_dir: /tmp

# Root credentials
mysql_root_password: "{{ vault_mysql_root_password }}"
mysql_root_password_update: false

# Network configuration
mysql_bind_address: "0.0.0.0"
mysql_port: 3306
mysql_max_connections: 200
mysql_max_user_connections: 0

# Performance tuning
mysql_innodb_buffer_pool_size: "1G"
mysql_innodb_log_file_size: "256M"
mysql_innodb_log_buffer_size: "16M"
mysql_innodb_flush_log_at_trx_commit: 1
mysql_innodb_file_per_table: 1
mysql_innodb_open_files: 400
mysql_query_cache_type: 0
mysql_query_cache_size: 0
mysql_tmp_table_size: "64M"
mysql_max_heap_table_size: "64M"
mysql_sort_buffer_size: "2M"
mysql_read_buffer_size: "128K"
mysql_read_rnd_buffer_size: "256K"
mysql_join_buffer_size: "128K"
mysql_thread_cache_size: 50
mysql_table_open_cache: 400
mysql_table_definition_cache: 400

# Logging
mysql_slow_query_log_enabled: true
mysql_slow_query_log_file: "{{ mysql_log_dir }}/mysql-slow.log"
mysql_long_query_time: 2
mysql_log_queries_not_using_indexes: false
mysql_general_log_enabled: false
mysql_general_log_file: "{{ mysql_log_dir }}/mysql.log"
mysql_error_log_file: "{{ mysql_log_dir }}/error.log"
mysql_log_error_verbosity: 2

# Binary logging and replication
mysql_log_bin_enabled: true
mysql_log_bin: mysql-bin
mysql_binlog_format: ROW
mysql_expire_logs_days: 7
mysql_max_binlog_size: "100M"
mysql_binlog_do_db: []
mysql_binlog_ignore_db: []

# Replication configuration
mysql_replication_enabled: false
mysql_replication_role: master  # master or slave
mysql_replication_master_host: ""
mysql_replication_master_port: 3306
mysql_replication_user: replicator
mysql_replication_password: "{{ vault_mysql_replication_password }}"
mysql_server_id: 1
mysql_auto_increment_increment: 1
mysql_auto_increment_offset: 1

# Galera cluster configuration
mysql_galera_enabled: false
mysql_galera_cluster_name: "galera_cluster"
mysql_galera_cluster_address: "gcomm://"
mysql_galera_node_name: "{{ inventory_hostname }}"
mysql_galera_node_address: "{{ ansible_default_ipv4.address }}"
mysql_galera_sst_method: rsync
mysql_galera_sst_user: sst
mysql_galera_sst_password: "{{ vault_mysql_galera_sst_password }}"

# Security settings
mysql_skip_name_resolve: true
mysql_local_infile: false
mysql_symbolic_links: false
mysql_secure_file_priv: "/var/lib/mysql-files"

# SSL configuration
mysql_ssl_enabled: false
mysql_ssl_ca: /etc/mysql/ssl/ca-cert.pem
mysql_ssl_cert: /etc/mysql/ssl/server-cert.pem
mysql_ssl_key: /etc/mysql/ssl/server-key.pem

# Backup configuration
mysql_backup_enabled: true
mysql_backup_user: backup
mysql_backup_password: "{{ vault_mysql_backup_password }}"
mysql_backup_dir: /opt/mysql-backups
mysql_backup_schedule: "0 2 * * *"
mysql_backup_retention_days: 7
mysql_backup_compress: true

# Monitoring
mysql_monitoring_enabled: true
mysql_exporter_enabled: true
mysql_exporter_port: 9104
mysql_exporter_user: exporter
mysql_exporter_password: "{{ vault_mysql_exporter_password }}"

# Databases to create
mysql_databases:
  - name: example_db
    encoding: utf8mb4
    collation: utf8mb4_unicode_ci

# Users to create
mysql_users:
  - name: example_user
    password: "{{ vault_mysql_example_password }}"
    priv: "example_db.*:ALL"
    host: "%"

# Custom configuration
mysql_custom_config: {}

# Firewall
mysql_firewall_enabled: true
mysql_firewall_allowed_hosts:
  - "10.0.0.0/8"
  - "**********/12"
  - "***********/16"
