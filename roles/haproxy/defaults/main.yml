---
# HAProxy role default variables

# HAProxy version and installation
haproxy_version: "2.8"
haproxy_package_state: present
haproxy_service_name: haproxy
haproxy_user: haproxy
haproxy_group: haproxy
haproxy_config_file: /etc/haproxy/haproxy.cfg
haproxy_config_dir: /etc/haproxy
haproxy_log_dir: /var/log/haproxy
haproxy_stats_dir: /var/lib/haproxy/stats

# Global configuration
haproxy_global_chroot: /var/lib/haproxy
haproxy_global_user: "{{ haproxy_user }}"
haproxy_global_group: "{{ haproxy_group }}"
haproxy_global_daemon: true
haproxy_global_nbproc: 1
haproxy_global_nbthread: 4
haproxy_global_maxconn: 4096
haproxy_global_ulimit_n: 65536

# SSL/TLS configuration
haproxy_ssl_enabled: true
haproxy_ssl_cert_dir: /etc/haproxy/certs
haproxy_ssl_default_bind_ciphers: "ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256"
haproxy_ssl_default_bind_options: "ssl-min-ver TLSv1.2 no-tls-tickets"

# Stats configuration
haproxy_stats_enabled: true
haproxy_stats_uri: /haproxy?stats
haproxy_stats_port: 8404
haproxy_stats_bind: "0.0.0.0"
haproxy_stats_user: admin
haproxy_stats_password: "{{ vault_haproxy_stats_password }}"
haproxy_stats_refresh: 30
haproxy_stats_hide_version: true

# Default timeouts
haproxy_timeout_connect: 5000ms
haproxy_timeout_client: 50000ms
haproxy_timeout_server: 50000ms
haproxy_timeout_http_request: 10s
haproxy_timeout_http_keep_alive: 2s
haproxy_timeout_check: 10s

# Load balancing algorithm
haproxy_balance_algorithm: roundrobin  # roundrobin, leastconn, source, uri, etc.

# Health check configuration
haproxy_health_check_enabled: true
haproxy_health_check_interval: 2s
haproxy_health_check_timeout: 1s
haproxy_health_check_rise: 2
haproxy_health_check_fall: 3

# Logging configuration
haproxy_log_enabled: true
haproxy_log_level: info
haproxy_log_facility: local0
haproxy_log_format: "%ci:%cp [%t] %ft %b/%s %Tw/%Tc/%Tt %B %ts %ac/%fc/%bc/%sc/%rc %sq/%bq %hr %hs %{+Q}r"

# Rate limiting
haproxy_rate_limit_enabled: false
haproxy_rate_limit_sessions: 10
haproxy_rate_limit_period: 10s

# Compression
haproxy_compression_enabled: true
haproxy_compression_algo: gzip
haproxy_compression_type: "text/html text/plain text/css text/javascript application/javascript application/json"

# Frontend configuration
haproxy_frontends:
  - name: web_frontend
    bind: "*:80"
    mode: http
    redirect: "scheme https code 301 if !{ ssl_fc }"
    default_backend: web_servers
  - name: web_frontend_ssl
    bind: "*:443 ssl crt {{ haproxy_ssl_cert_dir }}/server.pem"
    mode: http
    default_backend: web_servers
    options:
      - httplog
      - dontlognull
      - forwardfor
      - http-server-close

# Backend configuration
haproxy_backends:
  - name: web_servers
    mode: http
    balance: "{{ haproxy_balance_algorithm }}"
    options:
      - httpchk GET /health
      - http-server-close
      - forwardfor
    servers:
      - name: web1
        address: "************:80"
        check: true
        maxconn: 100
      - name: web2
        address: "************:80"
        check: true
        maxconn: 100

# ACL (Access Control Lists)
haproxy_acls:
  - name: is_api
    condition: "path_beg /api/"
  - name: is_admin
    condition: "path_beg /admin/"
  - name: allowed_ips
    condition: "src ***********/24 10.0.0.0/8"

# Stick tables for session persistence
haproxy_stick_tables:
  - name: session_table
    type: ip
    size: 100k
    expire: 30m

# Error pages
haproxy_error_pages:
  - code: 400
    file: /etc/haproxy/errors/400.http
  - code: 403
    file: /etc/haproxy/errors/403.http
  - code: 408
    file: /etc/haproxy/errors/408.http
  - code: 500
    file: /etc/haproxy/errors/500.http
  - code: 502
    file: /etc/haproxy/errors/502.http
  - code: 503
    file: /etc/haproxy/errors/503.http
  - code: 504
    file: /etc/haproxy/errors/504.http

# Monitoring and metrics
haproxy_monitoring_enabled: true
haproxy_exporter_enabled: true
haproxy_exporter_port: 9101

# High availability with keepalived
haproxy_keepalived_enabled: false
haproxy_keepalived_vip: "*************"
haproxy_keepalived_interface: eth0
haproxy_keepalived_priority: 100
haproxy_keepalived_state: MASTER  # MASTER or BACKUP

# Custom configuration
haproxy_custom_global_config: []
haproxy_custom_defaults_config: []
haproxy_custom_frontend_config: {}
haproxy_custom_backend_config: {}

# Firewall configuration
haproxy_firewall_enabled: true
haproxy_firewall_allowed_ports:
  - 80
  - 443
  - "{{ haproxy_stats_port }}"
