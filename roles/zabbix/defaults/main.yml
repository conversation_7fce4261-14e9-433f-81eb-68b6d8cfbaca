---
# Zabbix role default variables

# Zabbix version and installation
zabbix_version: "6.4"
zabbix_package_state: present
zabbix_server_service_name: zabbix-server
zabbix_agent_service_name: zabbix-agent
zabbix_web_service_name: zabbix-web

# Component installation flags
zabbix_server_enabled: true
zabbix_agent_enabled: true
zabbix_web_enabled: true
zabbix_proxy_enabled: false

# Database configuration
zabbix_database_type: mysql  # mysql, postgresql
zabbix_database_host: localhost
zabbix_database_port: 3306
zabbix_database_name: zabbix
zabbix_database_user: zabbix
zabbix_database_password: "{{ vault_zabbix_database_password }}"
zabbix_database_root_password: "{{ vault_mysql_root_password }}"

# Zabbix server configuration
zabbix_server_host: "{{ ansible_default_ipv4.address }}"
zabbix_server_port: 10051
zabbix_server_log_file: /var/log/zabbix/zabbix_server.log
zabbix_server_log_file_size: 100
zabbix_server_debug_level: 3
zabbix_server_pid_file: /var/run/zabbix/zabbix_server.pid
zabbix_server_socket_dir: /var/run/zabbix
zabbix_server_db_host: "{{ zabbix_database_host }}"
zabbix_server_db_name: "{{ zabbix_database_name }}"
zabbix_server_db_user: "{{ zabbix_database_user }}"
zabbix_server_db_password: "{{ zabbix_database_password }}"
zabbix_server_db_port: "{{ zabbix_database_port }}"

# Performance tuning
zabbix_server_start_pollers: 5
zabbix_server_start_ipmi_pollers: 0
zabbix_server_start_pollersunreachable: 1
zabbix_server_start_trappers: 5
zabbix_server_start_pingers: 1
zabbix_server_start_discoverers: 1
zabbix_server_start_httppollers: 1
zabbix_server_start_timers: 1
zabbix_server_start_escalators: 1
zabbix_server_start_alerters: 3
zabbix_server_start_preprocessors: 3
zabbix_server_start_lld_processors: 2
zabbix_server_cache_size: 8M
zabbix_server_cache_update_frequency: 60
zabbix_server_start_db_syncers: 4
zabbix_server_history_cache_size: 16M
zabbix_server_history_index_cache_size: 4M
zabbix_server_trend_cache_size: 4M
zabbix_server_value_cache_size: 8M

# Zabbix agent configuration
zabbix_agent_server: "{{ zabbix_server_host }}"
zabbix_agent_server_active: "{{ zabbix_server_host }}:{{ zabbix_server_port }}"
zabbix_agent_hostname: "{{ inventory_hostname }}"
zabbix_agent_host_metadata: "Linux"
zabbix_agent_listen_port: 10050
zabbix_agent_listen_ip: "0.0.0.0"
zabbix_agent_start_agents: 3
zabbix_agent_log_file: /var/log/zabbix/zabbix_agentd.log
zabbix_agent_log_file_size: 100
zabbix_agent_debug_level: 3
zabbix_agent_pid_file: /var/run/zabbix/zabbix_agentd.pid
zabbix_agent_timeout: 3
zabbix_agent_allow_root: 0
zabbix_agent_user: zabbix
zabbix_agent_include: /etc/zabbix/zabbix_agentd.d/*.conf

# User parameters
zabbix_agent_user_parameters:
  - key: "custom.disk.discovery"
    command: "/usr/local/bin/disk_discovery.sh"
  - key: "custom.service.status[*]"
    command: "systemctl is-active $1"

# Web interface configuration
zabbix_web_server: apache  # apache, nginx
zabbix_web_php_fpm: false
zabbix_web_vhost_port: 80
zabbix_web_vhost_ssl_port: 443
zabbix_web_context: zabbix
zabbix_web_server_name: "{{ inventory_hostname }}"

# PHP configuration
zabbix_php_max_execution_time: 300
zabbix_php_memory_limit: 128M
zabbix_php_post_max_size: 16M
zabbix_php_upload_max_filesize: 2M
zabbix_php_max_input_time: 300
zabbix_php_max_input_vars: 10000
zabbix_php_date_timezone: "UTC"

# SSL configuration
zabbix_web_ssl_enabled: false
zabbix_web_ssl_cert: /etc/ssl/certs/zabbix.crt
zabbix_web_ssl_key: /etc/ssl/private/zabbix.key

# Authentication
zabbix_web_admin_user: Admin
zabbix_web_admin_password: "{{ vault_zabbix_admin_password }}"

# LDAP authentication
zabbix_ldap_enabled: false
zabbix_ldap_host: ldap.example.com
zabbix_ldap_port: 389
zabbix_ldap_base_dn: "dc=example,dc=com"
zabbix_ldap_bind_dn: "cn=zabbix,ou=service,dc=example,dc=com"
zabbix_ldap_bind_password: "{{ vault_zabbix_ldap_password }}"
zabbix_ldap_search_attribute: uid

# Proxy configuration (if enabled)
zabbix_proxy_mode: 0  # 0 = active, 1 = passive
zabbix_proxy_server: "{{ zabbix_server_host }}"
zabbix_proxy_server_port: "{{ zabbix_server_port }}"
zabbix_proxy_hostname: "{{ inventory_hostname }}-proxy"
zabbix_proxy_log_file: /var/log/zabbix/zabbix_proxy.log
zabbix_proxy_log_file_size: 100
zabbix_proxy_debug_level: 3
zabbix_proxy_pid_file: /var/run/zabbix/zabbix_proxy.pid
zabbix_proxy_db_host: localhost
zabbix_proxy_db_name: zabbix_proxy
zabbix_proxy_db_user: zabbix_proxy
zabbix_proxy_db_password: "{{ vault_zabbix_proxy_db_password }}"

# Monitoring templates
zabbix_templates:
  - "Template OS Linux by Zabbix agent"
  - "Template App Apache by HTTP"
  - "Template App MySQL by Zabbix agent"
  - "Template App Nginx by HTTP"

# Host groups
zabbix_host_groups:
  - "Linux servers"
  - "Web servers"
  - "Database servers"
  - "Application servers"

# Actions and triggers
zabbix_actions:
  - name: "Send email on high CPU"
    event_source: 0  # triggers
    status: 0  # enabled
    conditions:
      - conditiontype: 4  # trigger value
        value: 1  # PROBLEM
    operations:
      - operationtype: 0  # send message
        opmessage:
          default_msg: 1
          mediatypeid: 1

# Media types
zabbix_media_types:
  - name: "Email"
    type: 0  # email
    smtp_server: "smtp.example.com"
    smtp_port: 587
    smtp_helo: "zabbix.example.com"
    smtp_email: "<EMAIL>"
    smtp_security: 2  # STARTTLS
    smtp_authentication: 1  # normal password
    username: "<EMAIL>"
    password: "{{ vault_zabbix_smtp_password }}"

# Custom scripts
zabbix_custom_scripts:
  - name: "restart_service.sh"
    content: |
      #!/bin/bash
      systemctl restart $1
    mode: '0755'

# Backup configuration
zabbix_backup_enabled: true
zabbix_backup_dir: /opt/zabbix-backups
zabbix_backup_schedule: "0 2 * * *"
zabbix_backup_retention_days: 30

# Firewall configuration
zabbix_firewall_enabled: true
zabbix_firewall_allowed_hosts:
  - "10.0.0.0/8"
  - "**********/12"
  - "***********/16"
