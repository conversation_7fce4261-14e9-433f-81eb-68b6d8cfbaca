#!/bin/bash
# Enterprise DevOps Stack Deployment Script
# Complete automation for enterprise-grade DevOps infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
INVENTORY_FILE="inventory/"
VAULT_PASSWORD_FILE=".vault_pass"
LOG_DIR="logs"
LOG_FILE="$LOG_DIR/enterprise-deployment-$(date +%Y%m%d-%H%M%S).log"
DEPLOYMENT_CONFIG="deployment-config.yml"

# Create logs directory
mkdir -p "$LOG_DIR"

# Function to print colored output
print_banner() {
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                    🚀 ENTERPRISE DEVOPS STACK DEPLOYMENT                    ║${NC}"
    echo -e "${PURPLE}║                         Complete Automation Solution                         ║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
}

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

print_header() {
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}" | tee -a "$LOG_FILE"
    echo -e "${BLUE}║ $1${NC}" | tee -a "$LOG_FILE"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}" | tee -a "$LOG_FILE"
}

print_step() {
    echo -e "${CYAN}▶ $1${NC}" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    print_header "🔍 CHECKING PREREQUISITES"
    
    local missing_tools=()
    
    # Check required tools
    for tool in ansible terraform docker kubectl helm; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        else
            print_info "✅ $tool is installed"
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_info "Please install missing tools and run again"
        exit 1
    fi
    
    # Check files
    for file in "$INVENTORY_FILE/hosts" "$VAULT_PASSWORD_FILE"; do
        if [ ! -f "$file" ]; then
            print_error "Required file not found: $file"
            exit 1
        else
            print_info "✅ $file exists"
        fi
    done
    
    print_info "✅ All prerequisites satisfied"
}

# Test connectivity
test_connectivity() {
    print_header "🌐 TESTING CONNECTIVITY"
    
    if ansible all -i "$INVENTORY_FILE" -m ping --vault-password-file "$VAULT_PASSWORD_FILE" >> "$LOG_FILE" 2>&1; then
        print_info "✅ Connectivity test passed"
    else
        print_error "❌ Connectivity test failed"
        exit 1
    fi
}

# Deploy infrastructure
deploy_infrastructure() {
    print_header "🏗️ DEPLOYING CORE INFRASTRUCTURE"
    
    print_step "Deploying base infrastructure..."
    ansible-playbook -i "$INVENTORY_FILE" playbooks/site.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        --extra-vars "serial=25%" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "✅ Core infrastructure deployment completed"
    else
        print_error "❌ Core infrastructure deployment failed"
        exit 1
    fi
}

# Deploy security stack
deploy_security() {
    print_header "🔒 DEPLOYING SECURITY STACK"
    
    print_step "Deploying HashiCorp Vault..."
    print_step "Configuring security hardening..."
    print_step "Setting up compliance monitoring..."
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/security-hardening.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "✅ Security stack deployment completed"
    else
        print_error "❌ Security stack deployment failed"
        exit 1
    fi
}

# Deploy Kubernetes
deploy_kubernetes() {
    print_header "☸️ DEPLOYING KUBERNETES CLUSTER"
    
    print_step "Setting up Kubernetes masters..."
    print_step "Joining worker nodes..."
    print_step "Installing CNI and ingress..."
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/kubernetes-cluster.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "✅ Kubernetes cluster deployment completed"
    else
        print_error "❌ Kubernetes cluster deployment failed"
        exit 1
    fi
}

# Deploy CI/CD
deploy_cicd() {
    print_header "🔄 DEPLOYING CI/CD PIPELINE"
    
    print_step "Installing Jenkins with plugins..."
    print_step "Configuring Docker integration..."
    print_step "Setting up pipeline templates..."
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/cicd-setup.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "✅ CI/CD pipeline deployment completed"
    else
        print_error "❌ CI/CD pipeline deployment failed"
        exit 1
    fi
}

# Deploy monitoring
deploy_monitoring() {
    print_header "📊 DEPLOYING MONITORING STACK"
    
    print_step "Installing Prometheus and Grafana..."
    print_step "Setting up ELK stack..."
    print_step "Configuring alerting..."
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/monitoring-stack.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "✅ Monitoring stack deployment completed"
    else
        print_error "❌ Monitoring stack deployment failed"
        exit 1
    fi
}

# Deploy observability
deploy_observability() {
    print_header "🔍 DEPLOYING OBSERVABILITY STACK"
    
    print_step "Installing Jaeger tracing..."
    print_step "Setting up OpenTelemetry..."
    print_step "Configuring distributed tracing..."
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/observability-stack.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "✅ Observability stack deployment completed"
    else
        print_error "❌ Observability stack deployment failed"
        exit 1
    fi
}

# Deploy DevOps tools
deploy_devops_tools() {
    print_header "🛠️ DEPLOYING DEVOPS TOOLS"
    
    print_step "Installing SonarQube..."
    print_step "Setting up Nexus Repository..."
    print_step "Configuring GitLab..."
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/devops-tools.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "✅ DevOps tools deployment completed"
    else
        print_error "❌ DevOps tools deployment failed"
        exit 1
    fi
}

# Deploy backup and DR
deploy_backup_dr() {
    print_header "💾 DEPLOYING BACKUP & DISASTER RECOVERY"
    
    print_step "Setting up backup infrastructure..."
    print_step "Configuring disaster recovery..."
    print_step "Testing backup procedures..."
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/backup-disaster-recovery.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "✅ Backup & DR deployment completed"
    else
        print_error "❌ Backup & DR deployment failed"
        exit 1
    fi
}

# Deploy multi-cloud
deploy_multicloud() {
    print_header "☁️ DEPLOYING MULTI-CLOUD INFRASTRUCTURE"
    
    print_step "Setting up cloud management tools..."
    print_step "Configuring hybrid networking..."
    print_step "Deploying cross-cloud services..."
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/multi-cloud-deployment.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "✅ Multi-cloud deployment completed"
    else
        print_error "❌ Multi-cloud deployment failed"
        exit 1
    fi
}

# Deploy with Terraform
deploy_terraform() {
    print_header "🏗️ DEPLOYING INFRASTRUCTURE AS CODE"
    
    print_step "Installing Terraform..."
    print_step "Provisioning cloud resources..."
    print_step "Updating Ansible inventory..."
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/terraform-integration.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "✅ Terraform deployment completed"
    else
        print_error "❌ Terraform deployment failed"
        exit 1
    fi
}

# Run validation tests
run_validation() {
    print_header "✅ RUNNING VALIDATION TESTS"
    
    print_step "Testing infrastructure..."
    print_step "Validating services..."
    print_step "Checking security..."
    
    ansible-playbook -i "$INVENTORY_FILE" tests/test_playbooks.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "✅ Validation tests passed"
    else
        print_warning "⚠️ Some validation tests failed - check logs"
    fi
}

# Generate deployment report
generate_report() {
    print_header "📋 GENERATING DEPLOYMENT REPORT"
    
    local report_file="$LOG_DIR/deployment-report-$(date +%Y%m%d-%H%M%S).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Enterprise DevOps Stack Deployment Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #2c3e50; color: white; padding: 20px; text-align: center; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #3498db; }
        .success { border-left-color: #27ae60; }
        .warning { border-left-color: #f39c12; }
        .error { border-left-color: #e74c3c; }
        .service-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .service-card { background: #f8f9fa; padding: 15px; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Enterprise DevOps Stack</h1>
        <h2>Deployment Report</h2>
        <p>Generated on: $(date)</p>
    </div>
    
    <div class="section success">
        <h3>✅ Deployment Summary</h3>
        <p>Enterprise DevOps stack has been successfully deployed with the following components:</p>
    </div>
    
    <div class="service-list">
        <div class="service-card">
            <h4>🏗️ Core Infrastructure</h4>
            <ul>
                <li>Web servers with Nginx</li>
                <li>Database clusters</li>
                <li>Load balancers</li>
                <li>Security hardening</li>
            </ul>
        </div>
        
        <div class="service-card">
            <h4>☸️ Container Orchestration</h4>
            <ul>
                <li>Kubernetes cluster</li>
                <li>Helm package manager</li>
                <li>Ingress controllers</li>
                <li>Service mesh (optional)</li>
            </ul>
        </div>
        
        <div class="service-card">
            <h4>🔄 CI/CD Pipeline</h4>
            <ul>
                <li>Jenkins automation server</li>
                <li>Docker containerization</li>
                <li>Pipeline templates</li>
                <li>Automated testing</li>
            </ul>
        </div>
        
        <div class="service-card">
            <h4>📊 Monitoring & Observability</h4>
            <ul>
                <li>Prometheus metrics</li>
                <li>Grafana dashboards</li>
                <li>ELK log management</li>
                <li>Jaeger distributed tracing</li>
            </ul>
        </div>
        
        <div class="service-card">
            <h4>🛠️ DevOps Tools</h4>
            <ul>
                <li>SonarQube code quality</li>
                <li>Nexus artifact repository</li>
                <li>GitLab source control</li>
                <li>Docker registry</li>
            </ul>
        </div>
        
        <div class="service-card">
            <h4>🔒 Security & Compliance</h4>
            <ul>
                <li>HashiCorp Vault</li>
                <li>Security scanning</li>
                <li>Compliance monitoring</li>
                <li>Certificate management</li>
            </ul>
        </div>
        
        <div class="service-card">
            <h4>💾 Backup & DR</h4>
            <ul>
                <li>Automated backups</li>
                <li>Disaster recovery</li>
                <li>Cross-region replication</li>
                <li>Recovery testing</li>
            </ul>
        </div>
        
        <div class="service-card">
            <h4>☁️ Multi-Cloud</h4>
            <ul>
                <li>AWS integration</li>
                <li>Azure support</li>
                <li>GCP connectivity</li>
                <li>Hybrid networking</li>
            </ul>
        </div>
    </div>
    
    <div class="section">
        <h3>🔗 Service URLs</h3>
        <p>Access your deployed services at the following URLs:</p>
        <ul>
            <li><strong>Jenkins:</strong> http://your-jenkins-server:8080</li>
            <li><strong>Grafana:</strong> http://your-monitoring-server:3000</li>
            <li><strong>Prometheus:</strong> http://your-monitoring-server:9090</li>
            <li><strong>Kibana:</strong> http://your-monitoring-server:5601</li>
            <li><strong>SonarQube:</strong> http://your-devops-server:9000</li>
            <li><strong>Nexus:</strong> http://your-devops-server:8081</li>
            <li><strong>Vault:</strong> https://your-vault-server:8200</li>
            <li><strong>Jaeger:</strong> http://your-monitoring-server:16686</li>
        </ul>
    </div>
    
    <div class="section">
        <h3>📚 Next Steps</h3>
        <ol>
            <li>Configure DNS entries for your services</li>
            <li>Set up SSL certificates</li>
            <li>Configure backup schedules</li>
            <li>Set up monitoring alerts</li>
            <li>Train your team on the new tools</li>
            <li>Implement your first CI/CD pipeline</li>
            <li>Set up disaster recovery testing</li>
        </ol>
    </div>
    
    <div class="section">
        <h3>📞 Support</h3>
        <p>For support and documentation, refer to:</p>
        <ul>
            <li>Project README.md</li>
            <li>Individual service documentation</li>
            <li>Troubleshooting guides</li>
            <li>Best practices documentation</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    print_info "📋 Deployment report generated: $report_file"
}

# Display final summary
show_final_summary() {
    print_header "🎉 DEPLOYMENT COMPLETE"
    
    echo -e "${GREEN}"
    cat << "EOF"
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🎉 ENTERPRISE DEVOPS STACK DEPLOYED!                     ║
║                                                                              ║
║  Your complete enterprise-grade DevOps infrastructure is now ready!         ║
║                                                                              ║
║  ✅ Core Infrastructure    ✅ Kubernetes Cluster     ✅ CI/CD Pipeline      ║
║  ✅ Security & Compliance  ✅ Monitoring Stack       ✅ DevOps Tools        ║
║  ✅ Backup & DR           ✅ Multi-Cloud Support     ✅ Observability       ║
║                                                                              ║
║  🚀 Ready for production workloads!                                         ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
    
    echo "📊 Deployment Statistics:" | tee -a "$LOG_FILE"
    echo "- Start time: $(head -1 "$LOG_FILE" | grep -o '[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\}-[0-9]\{2\}[0-9]\{2\}[0-9]\{2\}')" | tee -a "$LOG_FILE"
    echo "- End time: $(date +%Y%m%d-%H%M%S)" | tee -a "$LOG_FILE"
    echo "- Log file: $LOG_FILE" | tee -a "$LOG_FILE"
    echo "- Components deployed: 8 major stacks" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    
    print_info "🎯 Your enterprise DevOps platform is ready for action!"
    print_info "📖 Check the deployment report for detailed information"
    print_info "🚀 Start deploying your applications and enjoy automated DevOps!"
}

# Main execution function
main() {
    print_banner
    
    # Parse command line arguments
    DEPLOY_ALL=true
    SKIP_VALIDATION=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --infrastructure-only)
                DEPLOY_ALL=false
                DEPLOY_INFRASTRUCTURE=true
                shift
                ;;
            --security-only)
                DEPLOY_ALL=false
                DEPLOY_SECURITY=true
                shift
                ;;
            --kubernetes-only)
                DEPLOY_ALL=false
                DEPLOY_KUBERNETES=true
                shift
                ;;
            --cicd-only)
                DEPLOY_ALL=false
                DEPLOY_CICD=true
                shift
                ;;
            --monitoring-only)
                DEPLOY_ALL=false
                DEPLOY_MONITORING=true
                shift
                ;;
            --skip-validation)
                SKIP_VALIDATION=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --infrastructure-only  Deploy only core infrastructure"
                echo "  --security-only        Deploy only security stack"
                echo "  --kubernetes-only      Deploy only Kubernetes"
                echo "  --cicd-only           Deploy only CI/CD pipeline"
                echo "  --monitoring-only     Deploy only monitoring stack"
                echo "  --skip-validation     Skip validation tests"
                echo "  --help               Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Execute deployment steps
    check_prerequisites
    test_connectivity
    
    if [ "$DEPLOY_ALL" = true ]; then
        deploy_infrastructure
        deploy_security
        deploy_kubernetes
        deploy_cicd
        deploy_monitoring
        deploy_observability
        deploy_devops_tools
        deploy_backup_dr
        deploy_multicloud
        deploy_terraform
    else
        [ "$DEPLOY_INFRASTRUCTURE" = true ] && deploy_infrastructure
        [ "$DEPLOY_SECURITY" = true ] && deploy_security
        [ "$DEPLOY_KUBERNETES" = true ] && deploy_kubernetes
        [ "$DEPLOY_CICD" = true ] && deploy_cicd
        [ "$DEPLOY_MONITORING" = true ] && deploy_monitoring
    fi
    
    [ "$SKIP_VALIDATION" != true ] && run_validation
    
    generate_report
    show_final_summary
}

# Run main function with all arguments
main "$@"
