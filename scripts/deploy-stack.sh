#!/bin/bash
# Complete DevOps Stack Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
INVENTORY_FILE="inventory/"
VAULT_PASSWORD_FILE=".vault_pass"
LOG_FILE="logs/deployment-$(date +%Y%m%d-%H%M%S).log"

# Create logs directory
mkdir -p logs

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

print_header() {
    echo -e "${BLUE}========================================${NC}" | tee -a "$LOG_FILE"
    echo -e "${BLUE} $1${NC}" | tee -a "$LOG_FILE"
    echo -e "${BLUE}========================================${NC}" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if ansible is installed
    if ! command -v ansible &> /dev/null; then
        print_error "Ansible is not installed"
        exit 1
    fi
    
    # Check if inventory exists
    if [ ! -f "$INVENTORY_FILE/hosts" ]; then
        print_error "Inventory file not found: $INVENTORY_FILE/hosts"
        exit 1
    fi
    
    # Check if vault password file exists
    if [ ! -f "$VAULT_PASSWORD_FILE" ]; then
        print_error "Vault password file not found: $VAULT_PASSWORD_FILE"
        exit 1
    fi
    
    print_info "Prerequisites check passed"
}

# Test connectivity
test_connectivity() {
    print_header "Testing Connectivity"
    
    if ansible all -i "$INVENTORY_FILE" -m ping --vault-password-file "$VAULT_PASSWORD_FILE" >> "$LOG_FILE" 2>&1; then
        print_info "Connectivity test passed"
    else
        print_error "Connectivity test failed"
        exit 1
    fi
}

# Deploy infrastructure
deploy_infrastructure() {
    print_header "Deploying Core Infrastructure"
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/site.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        --extra-vars "serial=25%" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "Core infrastructure deployment completed"
    else
        print_error "Core infrastructure deployment failed"
        exit 1
    fi
}

# Deploy CI/CD
deploy_cicd() {
    print_header "Deploying CI/CD Infrastructure"
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/cicd-setup.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "CI/CD infrastructure deployment completed"
    else
        print_error "CI/CD infrastructure deployment failed"
        exit 1
    fi
}

# Deploy monitoring
deploy_monitoring() {
    print_header "Deploying Monitoring Stack"
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/monitoring-stack.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "Monitoring stack deployment completed"
    else
        print_error "Monitoring stack deployment failed"
        exit 1
    fi
}

# Deploy DevOps tools
deploy_devops_tools() {
    print_header "Deploying DevOps Tools"
    
    ansible-playbook -i "$INVENTORY_FILE" playbooks/devops-tools.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "DevOps tools deployment completed"
    else
        print_error "DevOps tools deployment failed"
        exit 1
    fi
}

# Run validation tests
run_validation() {
    print_header "Running Validation Tests"
    
    ansible-playbook -i "$INVENTORY_FILE" tests/test_playbooks.yml \
        --vault-password-file "$VAULT_PASSWORD_FILE" \
        | tee -a "$LOG_FILE"
    
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        print_info "Validation tests passed"
    else
        print_warning "Some validation tests failed - check logs"
    fi
}

# Display deployment summary
show_summary() {
    print_header "Deployment Summary"
    
    echo "Deployment completed at: $(date)" | tee -a "$LOG_FILE"
    echo "Log file: $LOG_FILE" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    echo "Services deployed:" | tee -a "$LOG_FILE"
    echo "- Core Infrastructure (Web servers, Databases, Load balancers)" | tee -a "$LOG_FILE"
    echo "- CI/CD Pipeline (Jenkins, Docker)" | tee -a "$LOG_FILE"
    echo "- Monitoring Stack (Prometheus, Grafana, ELK)" | tee -a "$LOG_FILE"
    echo "- DevOps Tools (SonarQube, Nexus, GitLab)" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    echo "Next steps:" | tee -a "$LOG_FILE"
    echo "1. Access Jenkins: http://your-jenkins-server:8080" | tee -a "$LOG_FILE"
    echo "2. Access Grafana: http://your-monitoring-server:3000" | tee -a "$LOG_FILE"
    echo "3. Configure your applications and pipelines" | tee -a "$LOG_FILE"
    echo "4. Set up monitoring alerts and dashboards" | tee -a "$LOG_FILE"
}

# Main execution
main() {
    print_header "DevOps Stack Deployment Started"
    
    # Parse command line arguments
    DEPLOY_ALL=true
    DEPLOY_INFRA=false
    DEPLOY_CICD=false
    DEPLOY_MONITORING=false
    DEPLOY_TOOLS=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --infra-only)
                DEPLOY_ALL=false
                DEPLOY_INFRA=true
                shift
                ;;
            --cicd-only)
                DEPLOY_ALL=false
                DEPLOY_CICD=true
                shift
                ;;
            --monitoring-only)
                DEPLOY_ALL=false
                DEPLOY_MONITORING=true
                shift
                ;;
            --tools-only)
                DEPLOY_ALL=false
                DEPLOY_TOOLS=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --infra-only      Deploy only core infrastructure"
                echo "  --cicd-only       Deploy only CI/CD components"
                echo "  --monitoring-only Deploy only monitoring stack"
                echo "  --tools-only      Deploy only DevOps tools"
                echo "  --help           Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Run deployment steps
    check_prerequisites
    test_connectivity
    
    if [ "$DEPLOY_ALL" = true ] || [ "$DEPLOY_INFRA" = true ]; then
        deploy_infrastructure
    fi
    
    if [ "$DEPLOY_ALL" = true ] || [ "$DEPLOY_CICD" = true ]; then
        deploy_cicd
    fi
    
    if [ "$DEPLOY_ALL" = true ] || [ "$DEPLOY_MONITORING" = true ]; then
        deploy_monitoring
    fi
    
    if [ "$DEPLOY_ALL" = true ] || [ "$DEPLOY_TOOLS" = true ]; then
        deploy_devops_tools
    fi
    
    run_validation
    show_summary
    
    print_info "🎉 DevOps stack deployment completed successfully!"
}

# Run main function
main "$@"
