#!/bin/bash
# Test script for Ansible project

set -e

echo "🧪 Running Ansible project tests..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    if ! command -v ansible &> /dev/null; then
        print_error "Ansible is not installed"
        exit 1
    fi
    
    if ! command -v ansible-lint &> /dev/null; then
        print_warning "ansible-lint is not installed, skipping lint checks"
        SKIP_LINT=true
    fi
    
    if ! command -v yamllint &> /dev/null; then
        print_warning "yamllint is not installed, skipping YAML lint checks"
        SKIP_YAML_LINT=true
    fi
}

# Run YAML lint
run_yaml_lint() {
    if [ "$SKIP_YAML_LINT" != "true" ]; then
        print_status "Running YAML lint..."
        yamllint .
    fi
}

# Run Ansible lint
run_ansible_lint() {
    if [ "$SKIP_LINT" != "true" ]; then
        print_status "Running Ansible lint..."
        ansible-lint playbooks/ roles/
    fi
}

# Test playbook syntax
test_syntax() {
    print_status "Testing playbook syntax..."
    
    for playbook in playbooks/*.yml; do
        if [ -f "$playbook" ]; then
            print_status "Checking syntax for $playbook"
            ansible-playbook --syntax-check "$playbook"
        fi
    done
}

# Test inventory
test_inventory() {
    print_status "Testing inventory..."
    ansible-inventory --list -i inventory/ > /dev/null
    ansible-inventory --graph -i inventory/
}

# Run validation playbook
run_validation() {
    print_status "Running validation tests..."
    if [ -f "tests/test_playbooks.yml" ]; then
        ansible-playbook -i inventory/ tests/test_playbooks.yml --check
    else
        print_warning "No validation playbook found"
    fi
}

# Main execution
main() {
    check_requirements
    run_yaml_lint
    run_ansible_lint
    test_syntax
    test_inventory
    run_validation
    
    print_status "✅ All tests completed successfully!"
}

# Run main function
main "$@"
