---
# Main site playbook - orchestrates all infrastructure
# Usage: ansible-playbook -i inventory/ playbooks/site.yml

- name: Configure all servers
  hosts: all
  become: yes
  gather_facts: yes
  serial: "{{ serial | default('100%') }}"
  
  pre_tasks:
    - name: Wait for system to become reachable
      wait_for_connection:
        timeout: 300
      tags: always

    - name: Gather facts
      setup:
      tags: always

    - name: Check if reboot is required
      stat:
        path: /var/run/reboot-required
      register: reboot_required_file
      tags: always

  roles:
    - role: common
      tags: common

  post_tasks:
    - name: Reboot if required
      reboot:
        msg: "Reboot initiated by Ansible for system updates"
        connect_timeout: 5
        reboot_timeout: 300
        pre_reboot_delay: 0
        post_reboot_delay: 30
      when: reboot_required_file.stat.exists
      tags: reboot

- name: Configure web servers
  hosts: webservers
  become: yes
  gather_facts: no
  serial: "{{ serial | default('50%') }}"
  
  roles:
    - role: webserver
      tags: webserver

- name: Configure database servers
  hosts: databases
  become: yes
  gather_facts: no
  serial: 1  # Deploy databases one at a time
  
  roles:
    - role: database
      tags: database

- name: Configure load balancers
  hosts: loadbalancers
  become: yes
  gather_facts: no
  
  roles:
    - role: loadbalancer
      tags: loadbalancer

- name: Configure monitoring servers
  hosts: monitoring
  become: yes
  gather_facts: no
  
  roles:
    - role: monitoring
      tags: monitoring
