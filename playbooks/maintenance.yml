---
# System maintenance playbook
# Usage: ansible-playbook -i inventory/ playbooks/maintenance.yml --tags "updates,cleanup"

- name: System maintenance tasks
  hosts: all
  become: yes
  gather_facts: yes
  serial: "{{ serial | default('25%') }}"
  
  vars:
    maintenance_mode: "{{ maintenance_mode | default(false) }}"
    update_packages: "{{ update_packages | default(true) }}"
    cleanup_logs: "{{ cleanup_logs | default(true) }}"
    cleanup_packages: "{{ cleanup_packages | default(true) }}"
    log_retention_days: "{{ log_retention_days | default(30) }}"
    
  pre_tasks:
    - name: Enable maintenance mode
      file:
        path: /var/www/html/maintenance.html
        state: touch
        owner: www-data
        group: www-data
        mode: '0644'
      when: maintenance_mode
      tags: maintenance

  tasks:
    - name: Update package cache
      package:
        update_cache: yes
      tags: updates

    - name: Upgrade all packages
      package:
        name: "*"
        state: latest
      when: update_packages
      register: package_updates
      tags: updates

    - name: Clean package cache
      command: "{{ ansible_pkg_mgr }} clean all"
      when: cleanup_packages and ansible_os_family == "RedHat"
      tags: cleanup

    - name: Clean package cache (Debian/Ubuntu)
      command: apt-get clean
      when: cleanup_packages and ansible_os_family == "Debian"
      tags: cleanup

    - name: Remove unnecessary packages
      command: "{{ ansible_pkg_mgr }} autoremove -y"
      when: cleanup_packages and ansible_os_family == "Debian"
      tags: cleanup

    - name: Clean old log files
      find:
        paths:
          - /var/log
          - /var/log/nginx
          - /var/log/apache2
        age: "{{ log_retention_days }}d"
        recurse: yes
        patterns: "*.log*"
      register: old_logs
      when: cleanup_logs
      tags: cleanup

    - name: Remove old log files
      file:
        path: "{{ item.path }}"
        state: absent
      with_items: "{{ old_logs.files }}"
      when: cleanup_logs and old_logs.files is defined
      tags: cleanup

    - name: Check disk usage
      shell: df -h
      register: disk_usage
      tags: monitoring

    - name: Display disk usage
      debug:
        var: disk_usage.stdout_lines
      tags: monitoring

    - name: Check memory usage
      shell: free -h
      register: memory_usage
      tags: monitoring

    - name: Display memory usage
      debug:
        var: memory_usage.stdout_lines
      tags: monitoring

    - name: Check for security updates
      shell: |
        if command -v unattended-upgrade >/dev/null 2>&1; then
          unattended-upgrade --dry-run
        elif command -v yum >/dev/null 2>&1; then
          yum --security check-update
        fi
      register: security_updates
      ignore_errors: yes
      tags: security

    - name: Restart services if needed
      service:
        name: "{{ item }}"
        state: restarted
      with_items:
        - nginx
        - apache2
      when: package_updates.changed
      ignore_errors: yes
      tags: restart

  post_tasks:
    - name: Disable maintenance mode
      file:
        path: /var/www/html/maintenance.html
        state: absent
      when: maintenance_mode
      tags: maintenance

    - name: Generate maintenance report
      template:
        src: maintenance_report.j2
        dest: "/tmp/maintenance_report_{{ ansible_date_time.date }}.txt"
      tags: reporting
