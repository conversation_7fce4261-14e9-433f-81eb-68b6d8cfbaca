---
# Advanced Observability Stack Deployment
# Usage: ansible-playbook -i inventory/ playbooks/observability-stack.yml

- name: Deploy Advanced Observability Stack
  hosts: monitoring_servers
  become: yes
  gather_facts: yes
  
  vars:
    observability_stack_path: /opt/observability
    jaeger_version: "1.49.0"
    otel_collector_version: "0.86.0"
    tempo_version: "2.2.3"
    loki_version: "2.9.1"
    
  pre_tasks:
    - name: Create observability directory structure
      file:
        path: "{{ observability_stack_path }}/{{ item }}"
        state: directory
        mode: '0755'
      with_items:
        - jaeger
        - otel-collector
        - tempo
        - loki
        - promtail
        - vector
        - configs
        - data
      tags: directories

  tasks:
    - name: Create advanced observability Docker Compose
      template:
        src: observability/docker-compose.yml.j2
        dest: "{{ observability_stack_path }}/docker-compose.yml"
        mode: '0644'
      tags: compose

    - name: Configure OpenTelemetry Collector
      template:
        src: observability/otel-collector-config.yml.j2
        dest: "{{ observability_stack_path }}/otel-collector/config.yml"
        mode: '0644'
      tags: otel_config

    - name: Configure Jaeger
      template:
        src: observability/jaeger-config.yml.j2
        dest: "{{ observability_stack_path }}/jaeger/config.yml"
        mode: '0644'
      tags: jaeger_config

    - name: Configure Tempo
      template:
        src: observability/tempo-config.yml.j2
        dest: "{{ observability_stack_path }}/tempo/config.yml"
        mode: '0644'
      tags: tempo_config

    - name: Configure Loki
      template:
        src: observability/loki-config.yml.j2
        dest: "{{ observability_stack_path }}/loki/config.yml"
        mode: '0644'
      tags: loki_config

    - name: Configure Promtail
      template:
        src: observability/promtail-config.yml.j2
        dest: "{{ observability_stack_path }}/promtail/config.yml"
        mode: '0644'
      tags: promtail_config

    - name: Configure Vector log processor
      template:
        src: observability/vector-config.toml.j2
        dest: "{{ observability_stack_path }}/vector/config.toml"
        mode: '0644'
      tags: vector_config

    - name: Create custom Grafana dashboards
      template:
        src: "observability/dashboards/{{ item }}.json.j2"
        dest: "{{ observability_stack_path }}/grafana/dashboards/{{ item }}.json"
        mode: '0644'
      with_items:
        - application-performance
        - distributed-tracing
        - log-analysis
        - infrastructure-overview
        - kubernetes-monitoring
        - security-monitoring
      tags: dashboards

    - name: Configure Prometheus with advanced rules
      template:
        src: observability/prometheus-advanced.yml.j2
        dest: "{{ observability_stack_path }}/prometheus/prometheus.yml"
        mode: '0644'
      tags: prometheus_config

    - name: Create advanced alerting rules
      template:
        src: observability/alert-rules.yml.j2
        dest: "{{ observability_stack_path }}/prometheus/alert-rules.yml"
        mode: '0644'
      tags: alert_rules

    - name: Deploy observability stack
      docker_compose:
        project_src: "{{ observability_stack_path }}"
        state: present
        pull: yes
      tags: deploy

    - name: Wait for services to be ready
      uri:
        url: "http://{{ ansible_default_ipv4.address }}:{{ item.port }}{{ item.path | default('') }}"
        method: GET
        status_code: [200, 404]  # Some services return 404 on root
      retries: 30
      delay: 10
      with_items:
        - { port: 16686, path: "/" }      # Jaeger UI
        - { port: 3200, path: "/ready" }  # Tempo
        - { port: 3100, path: "/ready" }  # Loki
        - { port: 4317 }                  # OTEL gRPC
        - { port: 4318 }                  # OTEL HTTP
      ignore_errors: yes
      tags: health_check

- name: Configure Application Instrumentation
  hosts: webservers
  become: yes
  gather_facts: yes
  
  vars:
    otel_collector_endpoint: "{{ groups['monitoring_servers'][0] }}:4317"
    
  tasks:
    - name: Install OpenTelemetry auto-instrumentation
      pip:
        name:
          - opentelemetry-distro
          - opentelemetry-exporter-otlp
          - opentelemetry-instrumentation
        state: present
      tags: otel_install

    - name: Configure OpenTelemetry environment
      template:
        src: observability/otel-env.sh.j2
        dest: /etc/profile.d/otel-env.sh
        mode: '0644'
      tags: otel_env

    - name: Install application performance monitoring agents
      include_tasks: "apm_{{ item }}.yml"
      with_items:
        - java
        - python
        - nodejs
        - golang
      when: "item in application_languages | default(['python'])"
      tags: apm_agents

    - name: Configure log forwarding to Loki
      template:
        src: observability/promtail-agent.yml.j2
        dest: /etc/promtail/config.yml
        mode: '0644'
      notify: restart promtail
      tags: log_forwarding

    - name: Install Promtail as systemd service
      template:
        src: observability/promtail.service.j2
        dest: /etc/systemd/system/promtail.service
        mode: '0644'
      notify:
        - reload systemd
        - restart promtail
      tags: promtail_service

- name: Deploy Custom Metrics and Monitoring
  hosts: all
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Install custom metrics exporters
      docker_container:
        name: "{{ item.name }}"
        image: "{{ item.image }}"
        ports: "{{ item.ports }}"
        volumes: "{{ item.volumes | default([]) }}"
        restart_policy: unless-stopped
        networks:
          - name: monitoring
      with_items:
        - name: node-exporter
          image: prom/node-exporter:latest
          ports: ["9100:9100"]
          volumes:
            - "/proc:/host/proc:ro"
            - "/sys:/host/sys:ro"
            - "/:/rootfs:ro"
        - name: cadvisor
          image: gcr.io/cadvisor/cadvisor:latest
          ports: ["8080:8080"]
          volumes:
            - "/:/rootfs:ro"
            - "/var/run:/var/run:ro"
            - "/sys:/sys:ro"
            - "/var/lib/docker/:/var/lib/docker:ro"
        - name: blackbox-exporter
          image: prom/blackbox-exporter:latest
          ports: ["9115:9115"]
      tags: exporters

    - name: Create custom application metrics script
      template:
        src: observability/custom-metrics.py.j2
        dest: /usr/local/bin/custom-metrics.py
        mode: '0755'
      tags: custom_metrics

    - name: Setup custom metrics collection cron job
      cron:
        name: "Collect custom metrics"
        minute: "*/5"
        job: "/usr/local/bin/custom-metrics.py"
      tags: custom_metrics

    - name: Configure business metrics dashboard
      template:
        src: observability/business-metrics.json.j2
        dest: /tmp/business-metrics-dashboard.json
        mode: '0644'
      tags: business_metrics

- name: Setup Distributed Tracing
  hosts: webservers
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Configure distributed tracing for applications
      template:
        src: "observability/tracing/{{ item }}-tracing.conf.j2"
        dest: "/etc/{{ item }}/tracing.conf"
        mode: '0644'
      with_items:
        - nginx
        - apache
        - haproxy
      when: "item in web_servers | default(['nginx'])"
      tags: tracing_config

    - name: Install tracing libraries
      package:
        name:
          - "{{ item }}"
        state: present
      with_items:
        - libjaeger-dev
        - libopentracing-dev
      when: ansible_os_family == "Debian"
      tags: tracing_libs

    - name: Configure service mesh tracing (if Istio is enabled)
      template:
        src: observability/istio-tracing.yml.j2
        dest: /tmp/istio-tracing.yml
        mode: '0644'
      when: service_mesh_enabled | default(false)
      tags: service_mesh_tracing

- name: Setup Alerting and Incident Response
  hosts: monitoring_servers
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Configure advanced alerting rules
      template:
        src: observability/advanced-alerts.yml.j2
        dest: "{{ observability_stack_path }}/prometheus/advanced-alerts.yml"
        mode: '0644'
      tags: advanced_alerts

    - name: Setup PagerDuty integration
      template:
        src: observability/pagerduty-config.yml.j2
        dest: "{{ observability_stack_path }}/alertmanager/pagerduty.yml"
        mode: '0644'
      when: pagerduty_enabled | default(false)
      tags: pagerduty

    - name: Configure Slack alerting
      template:
        src: observability/slack-alerts.yml.j2
        dest: "{{ observability_stack_path }}/alertmanager/slack.yml"
        mode: '0644'
      when: slack_alerts_enabled | default(true)
      tags: slack_alerts

    - name: Create incident response playbook
      template:
        src: observability/incident-response.md.j2
        dest: "{{ observability_stack_path }}/docs/incident-response.md"
        mode: '0644'
      tags: incident_response

    - name: Setup automated remediation scripts
      template:
        src: "observability/remediation/{{ item }}.sh.j2"
        dest: "{{ observability_stack_path }}/scripts/{{ item }}.sh"
        mode: '0755'
      with_items:
        - high-cpu-remediation
        - disk-cleanup
        - service-restart
        - scale-application
      tags: auto_remediation

  handlers:
    - name: reload systemd
      systemd:
        daemon_reload: yes

    - name: restart promtail
      service:
        name: promtail
        state: restarted

- name: Display Observability Stack Information
  hosts: monitoring_servers[0]
  gather_facts: no
  
  tasks:
    - name: Display observability services
      debug:
        msg: |
          🔍 Advanced Observability Stack Deployed Successfully!
          
          📊 Monitoring & Metrics:
          - Prometheus: http://{{ ansible_default_ipv4.address }}:9090
          - Grafana: http://{{ ansible_default_ipv4.address }}:3000
          - AlertManager: http://{{ ansible_default_ipv4.address }}:9093
          
          🔍 Distributed Tracing:
          - Jaeger UI: http://{{ ansible_default_ipv4.address }}:16686
          - Tempo: http://{{ ansible_default_ipv4.address }}:3200
          
          📝 Logging:
          - Loki: http://{{ ansible_default_ipv4.address }}:3100
          - Grafana Logs: http://{{ ansible_default_ipv4.address }}:3000/explore
          
          🔧 Data Collection:
          - OpenTelemetry Collector: {{ ansible_default_ipv4.address }}:4317 (gRPC)
          - OpenTelemetry Collector: {{ ansible_default_ipv4.address }}:4318 (HTTP)
          
          📈 Custom Dashboards:
          - Application Performance Monitoring
          - Distributed Tracing Analysis
          - Log Analysis and Correlation
          - Infrastructure Overview
          - Security Monitoring
          
          🚨 Alerting:
          - Advanced alerting rules configured
          - Slack/PagerDuty integration ready
          - Automated remediation scripts deployed
          
          📚 Documentation:
          - Incident Response Playbook: {{ observability_stack_path }}/docs/
          - Configuration Files: {{ observability_stack_path }}/configs/
      tags: summary
