---
# Application deployment playbook
# Usage: ansible-playbook -i inventory/ playbooks/deploy.yml -e "app_name=myapp app_version=1.0.0"

- name: Deploy application
  hosts: "{{ target_hosts | default('webservers') }}"
  become: yes
  gather_facts: yes
  serial: "{{ serial | default('50%') }}"
  
  vars:
    app_name: "{{ app_name | mandatory }}"
    app_version: "{{ app_version | default('latest') }}"
    app_user: "{{ app_user | default('deploy') }}"
    app_path: "/opt/{{ app_name }}"
    backup_path: "/opt/backups/{{ app_name }}"
    
  pre_tasks:
    - name: Validate required variables
      assert:
        that:
          - app_name is defined
          - app_name != ""
        fail_msg: "app_name must be defined and not empty"
      tags: always

    - name: Create application directories
      file:
        path: "{{ item }}"
        state: directory
        owner: "{{ app_user }}"
        group: "{{ app_user }}"
        mode: '0755'
      with_items:
        - "{{ app_path }}"
        - "{{ backup_path }}"
      tags: directories

  tasks:
    - name: Stop application service
      service:
        name: "{{ app_name }}"
        state: stopped
      ignore_errors: yes
      tags: deploy

    - name: Create backup of current version
      archive:
        path: "{{ app_path }}"
        dest: "{{ backup_path }}/{{ app_name }}-{{ ansible_date_time.epoch }}.tar.gz"
        owner: "{{ app_user }}"
        group: "{{ app_user }}"
      when: app_version != "latest"
      tags: backup

    - name: Download application package
      get_url:
        url: "{{ app_download_url }}/{{ app_name }}-{{ app_version }}.tar.gz"
        dest: "/tmp/{{ app_name }}-{{ app_version }}.tar.gz"
        owner: "{{ app_user }}"
        group: "{{ app_user }}"
      when: app_download_url is defined
      tags: download

    - name: Extract application
      unarchive:
        src: "/tmp/{{ app_name }}-{{ app_version }}.tar.gz"
        dest: "{{ app_path }}"
        owner: "{{ app_user }}"
        group: "{{ app_user }}"
        remote_src: yes
      when: app_download_url is defined
      tags: extract

    - name: Set application permissions
      file:
        path: "{{ app_path }}"
        owner: "{{ app_user }}"
        group: "{{ app_user }}"
        recurse: yes
      tags: permissions

    - name: Start application service
      service:
        name: "{{ app_name }}"
        state: started
        enabled: yes
      tags: deploy

    - name: Wait for application to be ready
      uri:
        url: "http://{{ ansible_default_ipv4.address }}:{{ app_port | default(8080) }}/health"
        method: GET
        status_code: 200
      retries: 30
      delay: 10
      when: app_health_check | default(true)
      tags: health_check

  post_tasks:
    - name: Clean up temporary files
      file:
        path: "/tmp/{{ app_name }}-{{ app_version }}.tar.gz"
        state: absent
      tags: cleanup

    - name: Send deployment notification
      mail:
        to: "{{ notification_email | default('<EMAIL>') }}"
        subject: "Deployment Complete: {{ app_name }} {{ app_version }}"
        body: "Application {{ app_name }} version {{ app_version }} has been successfully deployed to {{ inventory_hostname }}"
      when: send_notifications | default(false)
      tags: notification
