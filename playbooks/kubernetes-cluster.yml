---
# Kubernetes Cluster Setup Playbook
# Usage: ansible-playbook -i inventory/ playbooks/kubernetes-cluster.yml

- name: Setup Kubernetes Master Nodes
  hosts: kubernetes_masters
  become: yes
  gather_facts: yes
  serial: 1
  
  vars:
    kubernetes_master_node: true
    kubernetes_worker_node: false
    
  pre_tasks:
    - name: Update system packages
      package:
        update_cache: yes
        upgrade: dist
      tags: system_update

    - name: Set hostname
      hostname:
        name: "{{ inventory_hostname }}"
      tags: hostname

    - name: Add hostname to /etc/hosts
      lineinfile:
        path: /etc/hosts
        line: "{{ ansible_default_ipv4.address }} {{ inventory_hostname }}"
        state: present
      tags: hostname

  roles:
    - role: common
      tags: common
    - role: docker
      tags: docker
    - role: kubernetes
      tags: kubernetes

  post_tasks:
    - name: Get join command
      shell: kubeadm token create --print-join-command
      register: kubernetes_join_command
      when: inventory_hostname == groups['kubernetes_masters'][0]
      tags: join_command

    - name: Save join command
      copy:
        content: "{{ kubernetes_join_command.stdout }}"
        dest: /tmp/kubernetes_join_command
        mode: '0600'
      when: inventory_hostname == groups['kubernetes_masters'][0]
      tags: join_command

    - name: Fetch join command
      fetch:
        src: /tmp/kubernetes_join_command
        dest: /tmp/kubernetes_join_command
        flat: yes
      when: inventory_hostname == groups['kubernetes_masters'][0]
      tags: join_command

- name: Setup Kubernetes Worker Nodes
  hosts: kubernetes_workers
  become: yes
  gather_facts: yes
  serial: "50%"
  
  vars:
    kubernetes_master_node: false
    kubernetes_worker_node: true
    
  pre_tasks:
    - name: Update system packages
      package:
        update_cache: yes
        upgrade: dist
      tags: system_update

    - name: Set hostname
      hostname:
        name: "{{ inventory_hostname }}"
      tags: hostname

    - name: Add hostname to /etc/hosts
      lineinfile:
        path: /etc/hosts
        line: "{{ ansible_default_ipv4.address }} {{ inventory_hostname }}"
        state: present
      tags: hostname

  roles:
    - role: common
      tags: common
    - role: docker
      tags: docker
    - role: kubernetes
      tags: kubernetes

  post_tasks:
    - name: Copy join command to worker
      copy:
        src: /tmp/kubernetes_join_command
        dest: /tmp/kubernetes_join_command
        mode: '0600'
      tags: join

    - name: Join worker to cluster
      shell: "{{ lookup('file', '/tmp/kubernetes_join_command') }}"
      tags: join

- name: Deploy Kubernetes Applications
  hosts: kubernetes_masters[0]
  become: yes
  gather_facts: no
  
  tasks:
    - name: Create application manifests directory
      file:
        path: /opt/kubernetes/manifests
        state: directory
        mode: '0755'
      tags: manifests

    - name: Deploy sample applications
      template:
        src: "kubernetes/{{ item }}.yml.j2"
        dest: "/opt/kubernetes/manifests/{{ item }}.yml"
        mode: '0644'
      with_items:
        - nginx-deployment
        - redis-deployment
        - postgres-deployment
        - monitoring-stack
      tags: manifests

    - name: Apply Kubernetes manifests
      kubernetes.core.k8s:
        src: "/opt/kubernetes/manifests/{{ item }}.yml"
        state: present
        wait: true
        wait_timeout: 300
      with_items:
        - nginx-deployment
        - redis-deployment
        - postgres-deployment
        - monitoring-stack
      tags: deploy

    - name: Get cluster information
      kubernetes.core.k8s_info:
        api_version: v1
        kind: Node
      register: cluster_nodes
      tags: info

    - name: Display cluster status
      debug:
        msg: |
          Kubernetes Cluster Status:
          
          Nodes: {{ cluster_nodes.resources | length }}
          {% for node in cluster_nodes.resources %}
          - {{ node.metadata.name }}: {{ node.status.conditions | selectattr('type', 'equalto', 'Ready') | map(attribute='status') | first }}
          {% endfor %}
          
          Access Dashboard: https://{{ ansible_default_ipv4.address }}:30443
          
          Get admin token:
          kubectl -n kubernetes-dashboard create token admin-user
      tags: info
