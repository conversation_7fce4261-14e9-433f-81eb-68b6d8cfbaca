---
# Advanced Security Hardening Playbook
# Usage: ansible-playbook -i inventory/ playbooks/security-hardening.yml

- name: Deploy HashiCorp Vault
  hosts: vault_servers
  become: yes
  gather_facts: yes
  
  roles:
    - role: common
      tags: common
    - role: vault
      tags: vault

  post_tasks:
    - name: Display Vault information
      debug:
        msg: |
          Vault deployed successfully!
          
          Access Vault UI: https://{{ ansible_default_ipv4.address }}:8200
          
          Initialize Vault:
          vault operator init
          
          Unseal Vault:
          vault operator unseal <key1>
          vault operator unseal <key2>
          vault operator unseal <key3>
      tags: info

- name: Security Scanning and Compliance
  hosts: all
  become: yes
  gather_facts: yes
  
  vars:
    security_scan_enabled: true
    compliance_check_enabled: true
    vulnerability_scan_enabled: true
    
  tasks:
    - name: Install security scanning tools
      package:
        name:
          - lynis
          - chkrootkit
          - rkhunter
          - aide
          - fail2ban
          - clamav
          - clamav-daemon
        state: present
      tags: security_tools

    - name: Configure fail2ban
      template:
        src: security/jail.local.j2
        dest: /etc/fail2ban/jail.local
        backup: yes
      notify: restart fail2ban
      tags: fail2ban

    - name: Configure AIDE (Advanced Intrusion Detection Environment)
      template:
        src: security/aide.conf.j2
        dest: /etc/aide/aide.conf
        backup: yes
      tags: aide

    - name: Initialize AIDE database
      command: aide --init
      args:
        creates: /var/lib/aide/aide.db.new
      tags: aide

    - name: Move AIDE database
      command: mv /var/lib/aide/aide.db.new /var/lib/aide/aide.db
      args:
        creates: /var/lib/aide/aide.db
      tags: aide

    - name: Run Lynis security audit
      command: lynis audit system --quick
      register: lynis_output
      when: security_scan_enabled
      tags: lynis

    - name: Save Lynis report
      copy:
        content: "{{ lynis_output.stdout }}"
        dest: "/tmp/lynis-report-{{ ansible_date_time.date }}.txt"
      when: security_scan_enabled
      tags: lynis

    - name: Run rootkit scanner
      command: rkhunter --check --skip-keypress
      register: rkhunter_output
      ignore_errors: yes
      when: security_scan_enabled
      tags: rkhunter

    - name: Configure ClamAV antivirus
      template:
        src: security/clamd.conf.j2
        dest: /etc/clamav/clamd.conf
        backup: yes
      notify: restart clamav-daemon
      tags: clamav

    - name: Update ClamAV virus definitions
      command: freshclam
      ignore_errors: yes
      tags: clamav

    - name: Configure automatic security updates
      template:
        src: security/50unattended-upgrades.j2
        dest: /etc/apt/apt.conf.d/50unattended-upgrades
      when: ansible_os_family == "Debian"
      tags: auto_updates

    - name: Enable unattended upgrades
      template:
        src: security/20auto-upgrades.j2
        dest: /etc/apt/apt.conf.d/20auto-upgrades
      when: ansible_os_family == "Debian"
      tags: auto_updates

    - name: Configure kernel parameters for security
      sysctl:
        name: "{{ item.name }}"
        value: "{{ item.value }}"
        state: present
        reload: yes
      with_items:
        - { name: "net.ipv4.ip_forward", value: "0" }
        - { name: "net.ipv4.conf.all.send_redirects", value: "0" }
        - { name: "net.ipv4.conf.default.send_redirects", value: "0" }
        - { name: "net.ipv4.conf.all.accept_redirects", value: "0" }
        - { name: "net.ipv4.conf.default.accept_redirects", value: "0" }
        - { name: "net.ipv4.conf.all.secure_redirects", value: "0" }
        - { name: "net.ipv4.conf.default.secure_redirects", value: "0" }
        - { name: "net.ipv4.conf.all.log_martians", value: "1" }
        - { name: "net.ipv4.conf.default.log_martians", value: "1" }
        - { name: "net.ipv4.icmp_echo_ignore_broadcasts", value: "1" }
        - { name: "net.ipv4.icmp_ignore_bogus_error_responses", value: "1" }
        - { name: "net.ipv4.tcp_syncookies", value: "1" }
        - { name: "kernel.dmesg_restrict", value: "1" }
        - { name: "kernel.kptr_restrict", value: "2" }
      tags: kernel_hardening

    - name: Configure file permissions
      file:
        path: "{{ item.path }}"
        mode: "{{ item.mode }}"
        owner: "{{ item.owner | default('root') }}"
        group: "{{ item.group | default('root') }}"
      with_items:
        - { path: "/etc/passwd", mode: "0644" }
        - { path: "/etc/shadow", mode: "0640", group: "shadow" }
        - { path: "/etc/group", mode: "0644" }
        - { path: "/etc/gshadow", mode: "0640", group: "shadow" }
        - { path: "/etc/ssh/sshd_config", mode: "0600" }
        - { path: "/boot/grub/grub.cfg", mode: "0600" }
      tags: file_permissions

    - name: Remove unnecessary packages
      package:
        name:
          - telnet
          - rsh-client
          - rsh-redone-client
          - talk
          - ntalk
          - ypbind
          - nis
        state: absent
      tags: remove_packages

    - name: Disable unnecessary services
      service:
        name: "{{ item }}"
        state: stopped
        enabled: no
      with_items:
        - avahi-daemon
        - cups
        - nfs-common
        - rpcbind
      ignore_errors: yes
      tags: disable_services

  handlers:
    - name: restart fail2ban
      service:
        name: fail2ban
        state: restarted

    - name: restart clamav-daemon
      service:
        name: clamav-daemon
        state: restarted

- name: Certificate Management with Let's Encrypt
  hosts: webservers
  become: yes
  gather_facts: yes
  
  vars:
    certbot_enabled: true
    certbot_email: "<EMAIL>"
    certbot_domains:
      - "{{ domain_name }}"
      - "www.{{ domain_name }}"
    
  tasks:
    - name: Install Certbot
      package:
        name:
          - certbot
          - python3-certbot-nginx
        state: present
      when: certbot_enabled
      tags: certbot

    - name: Generate Let's Encrypt certificates
      command: >
        certbot --nginx --non-interactive --agree-tos
        --email {{ certbot_email }}
        -d {{ certbot_domains | join(' -d ') }}
      args:
        creates: "/etc/letsencrypt/live/{{ certbot_domains[0] }}/fullchain.pem"
      when: certbot_enabled
      tags: certbot

    - name: Setup certificate renewal cron job
      cron:
        name: "Renew Let's Encrypt certificates"
        minute: "0"
        hour: "2"
        job: "certbot renew --quiet --no-self-upgrade"
      when: certbot_enabled
      tags: certbot

- name: Compliance Reporting
  hosts: all
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Generate compliance report
      template:
        src: security/compliance-report.j2
        dest: "/tmp/compliance-report-{{ inventory_hostname }}-{{ ansible_date_time.date }}.html"
      tags: compliance

    - name: Collect security logs
      shell: |
        mkdir -p /tmp/security-logs
        cp /var/log/auth.log /tmp/security-logs/ 2>/dev/null || true
        cp /var/log/secure /tmp/security-logs/ 2>/dev/null || true
        cp /var/log/fail2ban.log /tmp/security-logs/ 2>/dev/null || true
        tar -czf /tmp/security-logs-{{ inventory_hostname }}-{{ ansible_date_time.date }}.tar.gz -C /tmp security-logs/
      tags: logs

    - name: Display security summary
      debug:
        msg: |
          Security Hardening Complete!
          
          Applied Security Measures:
          - Fail2ban intrusion prevention
          - AIDE file integrity monitoring
          - ClamAV antivirus protection
          - Kernel security parameters
          - File permission hardening
          - Unnecessary service removal
          - Automatic security updates
          {% if certbot_enabled %}
          - SSL/TLS certificates from Let's Encrypt
          {% endif %}
          
          Reports Generated:
          - Lynis security audit: /tmp/lynis-report-{{ ansible_date_time.date }}.txt
          - Compliance report: /tmp/compliance-report-{{ inventory_hostname }}-{{ ansible_date_time.date }}.html
          - Security logs: /tmp/security-logs-{{ inventory_hostname }}-{{ ansible_date_time.date }}.tar.gz
      tags: summary
