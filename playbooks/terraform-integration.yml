---
# Terraform Integration Playbook
# Usage: ansible-playbook -i inventory/ playbooks/terraform-integration.yml

- name: Install and Configure Terraform
  hosts: terraform_runners
  become: yes
  gather_facts: yes
  
  vars:
    terraform_version: "1.6.2"
    terraform_install_path: "/usr/local/bin"
    terraform_workspace: "/opt/terraform"
    
  tasks:
    - name: Create terraform user
      user:
        name: terraform
        system: yes
        shell: /bin/bash
        home: "{{ terraform_workspace }}"
        create_home: yes
      tags: user

    - name: Download Terraform
      get_url:
        url: "https://releases.hashicorp.com/terraform/{{ terraform_version }}/terraform_{{ terraform_version }}_linux_amd64.zip"
        dest: "/tmp/terraform_{{ terraform_version }}_linux_amd64.zip"
        mode: '0644'
      tags: download

    - name: Install unzip
      package:
        name: unzip
        state: present
      tags: prerequisites

    - name: Extract Terraform
      unarchive:
        src: "/tmp/terraform_{{ terraform_version }}_linux_amd64.zip"
        dest: "{{ terraform_install_path }}"
        remote_src: yes
        owner: root
        group: root
        mode: '0755'
      tags: install

    - name: Verify Terraform installation
      command: terraform version
      register: terraform_version_output
      changed_when: false
      tags: verify

    - name: Display Terraform version
      debug:
        var: terraform_version_output.stdout
      tags: verify

    - name: Create Terraform workspace directories
      file:
        path: "{{ terraform_workspace }}/{{ item }}"
        state: directory
        owner: terraform
        group: terraform
        mode: '0755'
      with_items:
        - environments
        - modules
        - scripts
        - state-backups
      tags: workspace

    - name: Copy Terraform configurations
      copy:
        src: "{{ item.src }}"
        dest: "{{ terraform_workspace }}/{{ item.dest }}"
        owner: terraform
        group: terraform
        mode: '0644'
      with_items:
        - { src: "terraform/aws/", dest: "environments/aws/" }
        - { src: "terraform/azure/", dest: "environments/azure/" }
        - { src: "terraform/gcp/", dest: "environments/gcp/" }
        - { src: "terraform/modules/", dest: "modules/" }
      tags: configuration

    - name: Create Terraform wrapper script
      template:
        src: terraform/terraform-wrapper.sh.j2
        dest: "{{ terraform_workspace }}/scripts/terraform-wrapper.sh"
        owner: terraform
        group: terraform
        mode: '0755'
      tags: scripts

    - name: Create Terraform state backup script
      template:
        src: terraform/backup-state.sh.j2
        dest: "{{ terraform_workspace }}/scripts/backup-state.sh"
        owner: terraform
        group: terraform
        mode: '0755'
      tags: scripts

    - name: Setup Terraform state backup cron job
      cron:
        name: "Backup Terraform state"
        minute: "0"
        hour: "2"
        job: "{{ terraform_workspace }}/scripts/backup-state.sh"
        user: terraform
      tags: backup

- name: Deploy Infrastructure with Terraform
  hosts: terraform_runners[0]
  become: yes
  become_user: terraform
  gather_facts: no
  
  vars:
    cloud_provider: "{{ cloud_provider | default('aws') }}"
    environment: "{{ environment | default('production') }}"
    terraform_workspace: "/opt/terraform"
    
  tasks:
    - name: Initialize Terraform
      terraform:
        project_path: "{{ terraform_workspace }}/environments/{{ cloud_provider }}"
        state: present
        force_init: yes
      register: terraform_init
      tags: init

    - name: Plan Terraform deployment
      terraform:
        project_path: "{{ terraform_workspace }}/environments/{{ cloud_provider }}"
        state: planned
        plan_file: "{{ terraform_workspace }}/terraform.plan"
        variables:
          environment: "{{ environment }}"
          project_name: "{{ project_name | default('devops-automation') }}"
      register: terraform_plan
      tags: plan

    - name: Display Terraform plan
      debug:
        var: terraform_plan.stdout
      tags: plan

    - name: Apply Terraform configuration
      terraform:
        project_path: "{{ terraform_workspace }}/environments/{{ cloud_provider }}"
        state: present
        plan_file: "{{ terraform_workspace }}/terraform.plan"
      register: terraform_apply
      when: terraform_auto_apply | default(false)
      tags: apply

    - name: Get Terraform outputs
      terraform:
        project_path: "{{ terraform_workspace }}/environments/{{ cloud_provider }}"
        state: present
      register: terraform_outputs
      tags: outputs

    - name: Save infrastructure outputs
      copy:
        content: "{{ terraform_outputs.outputs | to_nice_json }}"
        dest: "{{ terraform_workspace }}/infrastructure-outputs.json"
        mode: '0644'
      tags: outputs

    - name: Generate dynamic inventory from Terraform
      template:
        src: terraform/terraform-inventory.py.j2
        dest: "{{ terraform_workspace }}/scripts/terraform-inventory.py"
        mode: '0755'
      tags: inventory

- name: Update Ansible Inventory from Terraform
  hosts: localhost
  gather_facts: no
  
  vars:
    terraform_workspace: "/opt/terraform"
    
  tasks:
    - name: Read Terraform outputs
      slurp:
        src: "{{ terraform_workspace }}/infrastructure-outputs.json"
      register: terraform_outputs_raw
      delegate_to: "{{ groups['terraform_runners'][0] }}"
      tags: inventory

    - name: Parse Terraform outputs
      set_fact:
        terraform_outputs: "{{ terraform_outputs_raw.content | b64decode | from_json }}"
      tags: inventory

    - name: Generate dynamic inventory
      template:
        src: terraform/dynamic-inventory.yml.j2
        dest: "inventory/terraform-generated.yml"
        mode: '0644'
      tags: inventory

    - name: Update group variables with Terraform outputs
      template:
        src: terraform/terraform-vars.yml.j2
        dest: "group_vars/terraform_managed.yml"
        mode: '0644'
      tags: inventory

- name: Validate Infrastructure
  hosts: terraform_managed
  gather_facts: yes
  
  tasks:
    - name: Test connectivity to Terraform-managed hosts
      ping:
      tags: validate

    - name: Check cloud metadata
      uri:
        url: "http://***************/latest/meta-data/instance-id"
        timeout: 5
      register: instance_metadata
      ignore_errors: yes
      when: cloud_provider == "aws"
      tags: validate

    - name: Display instance information
      debug:
        msg: |
          Infrastructure Validation:
          - Host: {{ inventory_hostname }}
          - IP: {{ ansible_default_ipv4.address }}
          {% if instance_metadata is defined and not instance_metadata.failed %}
          - Instance ID: {{ instance_metadata.content }}
          {% endif %}
          - Cloud Provider: {{ cloud_provider }}
          - Environment: {{ environment }}
      tags: validate

- name: Deploy Applications to Terraform Infrastructure
  hosts: terraform_managed
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Install Docker on cloud instances
      include_role:
        name: docker
      when: "'webservers' in group_names"
      tags: docker

    - name: Deploy monitoring agents
      include_role:
        name: monitoring
      when: monitoring_enabled | default(true)
      tags: monitoring

    - name: Configure cloud-specific settings
      include_tasks: "cloud_config_{{ cloud_provider }}.yml"
      tags: cloud_config

    - name: Display deployment summary
      debug:
        msg: |
          Terraform Infrastructure Deployment Complete!
          
          Cloud Provider: {{ cloud_provider }}
          Environment: {{ environment }}
          
          Resources Created:
          {% if terraform_outputs.vpc_id is defined %}
          - VPC: {{ terraform_outputs.vpc_id.value }}
          {% endif %}
          {% if terraform_outputs.load_balancer_dns is defined %}
          - Load Balancer: {{ terraform_outputs.load_balancer_dns.value }}
          {% endif %}
          {% if terraform_outputs.database_endpoint is defined %}
          - Database: {{ terraform_outputs.database_endpoint.value }}
          {% endif %}
          
          Next Steps:
          1. Configure DNS to point to load balancer
          2. Deploy applications using Ansible
          3. Set up monitoring and alerting
          4. Configure backup and disaster recovery
      tags: summary
