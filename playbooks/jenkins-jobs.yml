---
# <PERSON> Jobs Automation Playbook
# Usage: ansible-playbook -i inventory/ playbooks/jenkins-jobs.yml -e "project_name=myapp"

- name: <PERSON>reate and Configure <PERSON> Jobs
  hosts: jenkins_servers
  become: yes
  gather_facts: yes
  
  vars:
    project_name: "{{ project_name | mandatory }}"
    git_repo_url: "{{ git_repo_url | default('https://github.com/your-org/' + project_name + '.git') }}"
    docker_registry: "{{ docker_registry | default('registry.example.com') }}"
    deployment_environment: "{{ deployment_environment | default('staging') }}"
    
  tasks:
    - name: Create Jenkins job directories
      file:
        path: "{{ jenkins_home }}/jobs/{{ item }}"
        state: directory
        owner: "{{ jenkins_user }}"
        group: "{{ jenkins_group }}"
        mode: '0755'
      with_items:
        - "{{ project_name }}-build"
        - "{{ project_name }}-test"
        - "{{ project_name }}-deploy"
        - "{{ project_name }}-pipeline"
      tags: job_directories

    - name: Create build job configuration
      template:
        src: jenkins-jobs/build-job.xml.j2
        dest: "{{ jenkins_home }}/jobs/{{ project_name }}-build/config.xml"
        owner: "{{ jenkins_user }}"
        group: "{{ jenkins_group }}"
        mode: '0644'
      notify: reload jenkins
      tags: build_job

    - name: Create test job configuration
      template:
        src: jenkins-jobs/test-job.xml.j2
        dest: "{{ jenkins_home }}/jobs/{{ project_name }}-test/config.xml"
        owner: "{{ jenkins_user }}"
        group: "{{ jenkins_group }}"
        mode: '0644'
      notify: reload jenkins
      tags: test_job

    - name: Create deployment job configuration
      template:
        src: jenkins-jobs/deploy-job.xml.j2
        dest: "{{ jenkins_home }}/jobs/{{ project_name }}-deploy/config.xml"
        owner: "{{ jenkins_user }}"
        group: "{{ jenkins_group }}"
        mode: '0644'
      notify: reload jenkins
      tags: deploy_job

    - name: Create pipeline job configuration
      template:
        src: jenkins-jobs/pipeline-job.xml.j2
        dest: "{{ jenkins_home }}/jobs/{{ project_name }}-pipeline/config.xml"
        owner: "{{ jenkins_user }}"
        group: "{{ jenkins_group }}"
        mode: '0644'
      notify: reload jenkins
      tags: pipeline_job

    - name: Create Jenkinsfile for pipeline
      template:
        src: jenkins-jobs/Jenkinsfile.j2
        dest: "{{ jenkins_home }}/jobs/{{ project_name }}-pipeline/Jenkinsfile"
        owner: "{{ jenkins_user }}"
        group: "{{ jenkins_group }}"
        mode: '0644'
      tags: jenkinsfile

    - name: Create Docker build script
      template:
        src: scripts/docker-build.sh.j2
        dest: "{{ jenkins_home }}/scripts/{{ project_name }}-docker-build.sh"
        owner: "{{ jenkins_user }}"
        group: "{{ jenkins_group }}"
        mode: '0755'
      tags: docker_script

    - name: Create deployment script
      template:
        src: scripts/deploy.sh.j2
        dest: "{{ jenkins_home }}/scripts/{{ project_name }}-deploy.sh"
        owner: "{{ jenkins_user }}"
        group: "{{ jenkins_group }}"
        mode: '0755'
      tags: deploy_script

  handlers:
    - name: reload jenkins
      uri:
        url: "http://{{ jenkins_host }}:{{ jenkins_port }}/reload"
        method: POST
        user: "{{ jenkins_admin_username }}"
        password: "{{ jenkins_admin_password }}"
        force_basic_auth: yes
        status_code: [200, 302]
      ignore_errors: yes
