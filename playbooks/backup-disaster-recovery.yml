---
# Comprehensive Backup and Disaster Recovery Playbook
# Usage: ansible-playbook -i inventory/ playbooks/backup-disaster-recovery.yml

- name: Setup Backup Infrastructure
  hosts: backup_servers
  become: yes
  gather_facts: yes
  
  vars:
    backup_base_path: /opt/backups
    restic_version: "0.16.0"
    velero_version: "1.11.1"
    
  pre_tasks:
    - name: Create backup directory structure
      file:
        path: "{{ backup_base_path }}/{{ item }}"
        state: directory
        mode: '0755'
      with_items:
        - databases
        - applications
        - configurations
        - kubernetes
        - logs
        - monitoring
        - scripts
        - restore-points
      tags: directories

  tasks:
    - name: Install backup tools
      package:
        name:
          - rsync
          - duplicity
          - borgbackup
          - rclone
          - awscli
          - postgresql-client
          - mysql-client
        state: present
      tags: backup_tools

    - name: Install Restic backup tool
      get_url:
        url: "https://github.com/restic/restic/releases/download/v{{ restic_version }}/restic_{{ restic_version }}_linux_amd64.bz2"
        dest: "/tmp/restic.bz2"
        mode: '0644'
      tags: restic

    - name: Extract and install Restic
      shell: |
        cd /tmp
        bunzip2 restic.bz2
        chmod +x restic
        mv restic /usr/local/bin/
      args:
        creates: /usr/local/bin/restic
      tags: restic

    - name: Create backup configuration
      template:
        src: backup/backup-config.yml.j2
        dest: "{{ backup_base_path }}/config.yml"
        mode: '0600'
      tags: config

    - name: Create database backup scripts
      template:
        src: "backup/{{ item }}-backup.sh.j2"
        dest: "{{ backup_base_path }}/scripts/{{ item }}-backup.sh"
        mode: '0755'
      with_items:
        - mysql
        - postgresql
        - mongodb
        - redis
      tags: db_backup_scripts

    - name: Create application backup script
      template:
        src: backup/application-backup.sh.j2
        dest: "{{ backup_base_path }}/scripts/application-backup.sh"
        mode: '0755'
      tags: app_backup_script

    - name: Create configuration backup script
      template:
        src: backup/config-backup.sh.j2
        dest: "{{ backup_base_path }}/scripts/config-backup.sh"
        mode: '0755'
      tags: config_backup_script

    - name: Create comprehensive backup script
      template:
        src: backup/full-backup.sh.j2
        dest: "{{ backup_base_path }}/scripts/full-backup.sh"
        mode: '0755'
      tags: full_backup_script

    - name: Setup backup scheduling
      cron:
        name: "{{ item.name }}"
        minute: "{{ item.minute }}"
        hour: "{{ item.hour }}"
        day: "{{ item.day | default('*') }}"
        job: "{{ backup_base_path }}/scripts/{{ item.script }}"
        user: root
      with_items:
        - { name: "Database backup", minute: "0", hour: "1", script: "mysql-backup.sh" }
        - { name: "Application backup", minute: "0", hour: "2", script: "application-backup.sh" }
        - { name: "Configuration backup", minute: "0", hour: "3", script: "config-backup.sh" }
        - { name: "Full system backup", minute: "0", hour: "4", day: "0", script: "full-backup.sh" }
      tags: backup_schedule

- name: Setup Kubernetes Backup (Velero)
  hosts: kubernetes_masters[0]
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Download Velero CLI
      get_url:
        url: "https://github.com/vmware-tanzu/velero/releases/download/v{{ velero_version }}/velero-v{{ velero_version }}-linux-amd64.tar.gz"
        dest: "/tmp/velero.tar.gz"
        mode: '0644'
      tags: velero

    - name: Extract Velero CLI
      unarchive:
        src: "/tmp/velero.tar.gz"
        dest: "/tmp"
        remote_src: yes
      tags: velero

    - name: Install Velero CLI
      copy:
        src: "/tmp/velero-v{{ velero_version }}-linux-amd64/velero"
        dest: "/usr/local/bin/velero"
        mode: '0755'
        remote_src: yes
      tags: velero

    - name: Create Velero namespace
      kubernetes.core.k8s:
        name: velero
        api_version: v1
        kind: Namespace
        state: present
      tags: velero_namespace

    - name: Create Velero backup storage configuration
      template:
        src: backup/velero-config.yml.j2
        dest: "/tmp/velero-config.yml"
        mode: '0644'
      tags: velero_config

    - name: Install Velero in Kubernetes
      shell: |
        velero install \
          --provider aws \
          --plugins velero/velero-plugin-for-aws:v1.7.1 \
          --bucket {{ velero_backup_bucket }} \
          --backup-location-config region={{ aws_region }} \
          --snapshot-location-config region={{ aws_region }} \
          --secret-file /tmp/velero-credentials
      args:
        creates: /tmp/velero-installed
      tags: velero_install

    - name: Create Velero backup schedules
      kubernetes.core.k8s:
        definition:
          apiVersion: velero.io/v1
          kind: Schedule
          metadata:
            name: "{{ item.name }}"
            namespace: velero
          spec:
            schedule: "{{ item.schedule }}"
            template:
              includedNamespaces: "{{ item.namespaces }}"
              ttl: "{{ item.ttl }}"
      with_items:
        - name: daily-backup
          schedule: "0 2 * * *"
          namespaces: ["default", "production", "staging"]
          ttl: "720h"  # 30 days
        - name: weekly-backup
          schedule: "0 3 * * 0"
          namespaces: ["*"]
          ttl: "2160h"  # 90 days
      tags: velero_schedules

- name: Setup Disaster Recovery
  hosts: all
  become: yes
  gather_facts: yes
  
  vars:
    dr_base_path: /opt/disaster-recovery
    
  tasks:
    - name: Create disaster recovery directory
      file:
        path: "{{ dr_base_path }}/{{ item }}"
        state: directory
        mode: '0755'
      with_items:
        - scripts
        - runbooks
        - configs
        - tests
      tags: dr_directories

    - name: Create disaster recovery runbooks
      template:
        src: "disaster-recovery/{{ item }}.md.j2"
        dest: "{{ dr_base_path }}/runbooks/{{ item }}.md"
        mode: '0644'
      with_items:
        - database-recovery
        - application-recovery
        - infrastructure-recovery
        - network-recovery
        - security-incident-response
      tags: dr_runbooks

    - name: Create automated recovery scripts
      template:
        src: "disaster-recovery/{{ item }}.sh.j2"
        dest: "{{ dr_base_path }}/scripts/{{ item }}.sh"
        mode: '0755'
      with_items:
        - database-restore
        - application-restore
        - config-restore
        - full-system-restore
        - failover-script
      tags: dr_scripts

    - name: Create disaster recovery test script
      template:
        src: disaster-recovery/dr-test.sh.j2
        dest: "{{ dr_base_path }}/scripts/dr-test.sh"
        mode: '0755'
      tags: dr_test

    - name: Setup disaster recovery monitoring
      template:
        src: disaster-recovery/dr-monitoring.py.j2
        dest: "{{ dr_base_path }}/scripts/dr-monitoring.py"
        mode: '0755'
      tags: dr_monitoring

    - name: Create RTO/RPO monitoring dashboard
      template:
        src: disaster-recovery/rto-rpo-dashboard.json.j2
        dest: "{{ dr_base_path }}/configs/rto-rpo-dashboard.json"
        mode: '0644'
      tags: rto_rpo_dashboard

- name: Setup Cross-Region Replication
  hosts: primary_region
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Configure database replication
      include_tasks: "replication/{{ item }}-replication.yml"
      with_items:
        - mysql
        - postgresql
        - mongodb
      when: "item in database_engines | default(['mysql'])"
      tags: db_replication

    - name: Setup file synchronization to DR site
      template:
        src: disaster-recovery/file-sync.sh.j2
        dest: "{{ dr_base_path }}/scripts/file-sync.sh"
        mode: '0755'
      tags: file_sync

    - name: Configure cross-region backup replication
      cron:
        name: "Cross-region backup sync"
        minute: "0"
        hour: "*/6"
        job: "{{ dr_base_path }}/scripts/file-sync.sh"
      tags: backup_replication

- name: Setup Backup Monitoring and Alerting
  hosts: monitoring_servers
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Create backup monitoring script
      template:
        src: backup/backup-monitoring.py.j2
        dest: /usr/local/bin/backup-monitoring.py
        mode: '0755'
      tags: backup_monitoring

    - name: Setup backup monitoring cron job
      cron:
        name: "Backup monitoring"
        minute: "*/15"
        job: "/usr/local/bin/backup-monitoring.py"
      tags: backup_monitoring_cron

    - name: Create backup alerting rules
      template:
        src: backup/backup-alerts.yml.j2
        dest: "{{ observability_stack_path | default('/opt/observability') }}/prometheus/backup-alerts.yml"
        mode: '0644'
      tags: backup_alerts

    - name: Create backup dashboard
      template:
        src: backup/backup-dashboard.json.j2
        dest: "{{ observability_stack_path | default('/opt/observability') }}/grafana/dashboards/backup-dashboard.json"
        mode: '0644'
      tags: backup_dashboard

- name: Test Disaster Recovery Procedures
  hosts: dr_test_servers
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Run disaster recovery test
      shell: "{{ dr_base_path }}/scripts/dr-test.sh"
      register: dr_test_result
      tags: dr_test

    - name: Generate DR test report
      template:
        src: disaster-recovery/dr-test-report.html.j2
        dest: "{{ dr_base_path }}/reports/dr-test-{{ ansible_date_time.date }}.html"
        mode: '0644'
      tags: dr_report

    - name: Validate backup integrity
      shell: |
        restic -r {{ backup_repository }} check
        echo "Backup integrity check completed"
      register: backup_integrity_check
      ignore_errors: yes
      tags: backup_validation

    - name: Test restore procedures
      include_tasks: "disaster-recovery/test-restore-{{ item }}.yml"
      with_items:
        - database
        - application
        - configuration
      tags: restore_test

- name: Display Backup and DR Summary
  hosts: backup_servers[0]
  gather_facts: no
  
  tasks:
    - name: Display backup and disaster recovery information
      debug:
        msg: |
          💾 Backup and Disaster Recovery Setup Complete!
          
          📋 Backup Strategy:
          - Database backups: Daily at 1:00 AM
          - Application backups: Daily at 2:00 AM
          - Configuration backups: Daily at 3:00 AM
          - Full system backups: Weekly on Sunday at 4:00 AM
          
          🔄 Backup Tools Configured:
          - Restic for incremental backups
          - Velero for Kubernetes backups
          - Database-specific backup tools
          - Cross-region replication
          
          🚨 Disaster Recovery:
          - Automated recovery scripts deployed
          - DR runbooks created
          - RTO/RPO monitoring configured
          - Cross-region replication active
          
          📊 Monitoring:
          - Backup success/failure monitoring
          - Backup size and duration tracking
          - DR test automation
          - Alerting for backup failures
          
          📍 Key Locations:
          - Backup scripts: {{ backup_base_path }}/scripts/
          - DR runbooks: {{ dr_base_path }}/runbooks/
          - Recovery scripts: {{ dr_base_path }}/scripts/
          - Test reports: {{ dr_base_path }}/reports/
          
          🔧 Next Steps:
          1. Test restore procedures regularly
          2. Update DR runbooks as infrastructure changes
          3. Conduct DR drills quarterly
          4. Monitor backup success rates
          5. Validate cross-region replication
      tags: summary
