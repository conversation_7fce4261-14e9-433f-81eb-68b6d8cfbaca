---
# Monitoring Stack Setup Playbook
# Usage: ansible-playbook -i inventory/ playbooks/monitoring-stack.yml

- name: Deploy Monitoring Stack with Docker Compose
  hosts: monitoring_servers
  become: yes
  gather_facts: yes
  
  vars:
    monitoring_stack_path: /opt/monitoring
    grafana_admin_password: "{{ vault_grafana_admin_password }}"
    prometheus_retention: "30d"
    elasticsearch_heap_size: "1g"
    
  pre_tasks:
    - name: Create monitoring directory
      file:
        path: "{{ monitoring_stack_path }}"
        state: directory
        mode: '0755'
      tags: directories

    - name: Create monitoring subdirectories
      file:
        path: "{{ monitoring_stack_path }}/{{ item }}"
        state: directory
        mode: '0755'
      with_items:
        - prometheus
        - grafana
        - elasticsearch
        - kibana
        - logstash
        - alertmanager
      tags: directories

  tasks:
    - name: Create Docker Compose file for monitoring stack
      template:
        src: monitoring/docker-compose.yml.j2
        dest: "{{ monitoring_stack_path }}/docker-compose.yml"
        mode: '0644'
      tags: compose

    - name: Create Prometheus configuration
      template:
        src: monitoring/prometheus.yml.j2
        dest: "{{ monitoring_stack_path }}/prometheus/prometheus.yml"
        mode: '0644'
      tags: prometheus_config

    - name: Create Grafana provisioning configuration
      template:
        src: monitoring/grafana-datasources.yml.j2
        dest: "{{ monitoring_stack_path }}/grafana/datasources.yml"
        mode: '0644'
      tags: grafana_config

    - name: Create Logstash configuration
      template:
        src: monitoring/logstash.conf.j2
        dest: "{{ monitoring_stack_path }}/logstash/logstash.conf"
        mode: '0644'
      tags: logstash_config

    - name: Create Alertmanager configuration
      template:
        src: monitoring/alertmanager.yml.j2
        dest: "{{ monitoring_stack_path }}/alertmanager/alertmanager.yml"
        mode: '0644'
      tags: alertmanager_config

    - name: Start monitoring stack
      docker_compose:
        project_src: "{{ monitoring_stack_path }}"
        state: present
        pull: yes
      tags: deploy

    - name: Wait for services to be ready
      uri:
        url: "http://{{ ansible_default_ipv4.address }}:{{ item.port }}{{ item.path | default('') }}"
        method: GET
        status_code: 200
      retries: 30
      delay: 10
      with_items:
        - { port: 9090, path: "/-/ready" }  # Prometheus
        - { port: 3000, path: "/api/health" }  # Grafana
        - { port: 9200, path: "/_cluster/health" }  # Elasticsearch
        - { port: 5601, path: "/api/status" }  # Kibana
      tags: health_check

    - name: Create monitoring service status script
      template:
        src: monitoring/monitoring-status.sh.j2
        dest: /usr/local/bin/monitoring-status.sh
        mode: '0755'
      tags: scripts

    - name: Display monitoring services information
      debug:
        msg: |
          Monitoring Stack Deployed Successfully!
          
          Services Available:
          - Prometheus: http://{{ ansible_default_ipv4.address }}:9090
          - Grafana: http://{{ ansible_default_ipv4.address }}:3000 (admin/{{ grafana_admin_password }})
          - Elasticsearch: http://{{ ansible_default_ipv4.address }}:9200
          - Kibana: http://{{ ansible_default_ipv4.address }}:5601
          - Alertmanager: http://{{ ansible_default_ipv4.address }}:9093
      tags: info
