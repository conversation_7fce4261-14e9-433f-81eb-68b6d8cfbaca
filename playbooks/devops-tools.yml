---
# DevOps Tools Stack Setup Playbook
# Usage: ansible-playbook -i inventory/ playbooks/devops-tools.yml

- name: Deploy DevOps Tools Stack
  hosts: devops_tools
  become: yes
  gather_facts: yes
  
  vars:
    tools_stack_path: /opt/devops-tools
    sonarqube_version: "9.9-community"
    nexus_version: "3.39.0"
    gitlab_version: "16.2.4-ce.0"
    
  pre_tasks:
    - name: Create DevOps tools directory
      file:
        path: "{{ tools_stack_path }}"
        state: directory
        mode: '0755'
      tags: directories

    - name: Create tool subdirectories
      file:
        path: "{{ tools_stack_path }}/{{ item }}"
        state: directory
        mode: '0755'
      with_items:
        - sonarqube
        - nexus
        - gitlab
        - registry
      tags: directories

  tasks:
    - name: Create Docker Compose file for DevOps tools
      template:
        src: devops-tools/docker-compose.yml.j2
        dest: "{{ tools_stack_path }}/docker-compose.yml"
        mode: '0644'
      tags: compose

    - name: Create SonarQube configuration
      template:
        src: devops-tools/sonar.properties.j2
        dest: "{{ tools_stack_path }}/sonarqube/sonar.properties"
        mode: '0644'
      tags: sonarqube_config

    - name: Create Nexus configuration
      template:
        src: devops-tools/nexus.properties.j2
        dest: "{{ tools_stack_path }}/nexus/nexus.properties"
        mode: '0644'
      tags: nexus_config

    - name: Create GitLab configuration
      template:
        src: devops-tools/gitlab.rb.j2
        dest: "{{ tools_stack_path }}/gitlab/gitlab.rb"
        mode: '0644'
      tags: gitlab_config

    - name: Create Docker Registry configuration
      template:
        src: devops-tools/registry-config.yml.j2
        dest: "{{ tools_stack_path }}/registry/config.yml"
        mode: '0644'
      tags: registry_config

    - name: Start DevOps tools stack
      docker_compose:
        project_src: "{{ tools_stack_path }}"
        state: present
        pull: yes
      tags: deploy

    - name: Wait for services to be ready
      uri:
        url: "http://{{ ansible_default_ipv4.address }}:{{ item.port }}{{ item.path | default('') }}"
        method: GET
        status_code: [200, 302]
      retries: 60
      delay: 10
      with_items:
        - { port: 9000, path: "/api/system/status" }  # SonarQube
        - { port: 8081, path: "/service/rest/v1/status" }  # Nexus
        - { port: 80, path: "/-/health" }  # GitLab
        - { port: 5000, path: "/v2/" }  # Docker Registry
      ignore_errors: yes
      tags: health_check

    - name: Create DevOps tools management scripts
      template:
        src: devops-tools/{{ item }}.sh.j2
        dest: "/usr/local/bin/{{ item }}.sh"
        mode: '0755'
      with_items:
        - devops-backup
        - devops-restore
        - devops-status
      tags: scripts

    - name: Display DevOps tools information
      debug:
        msg: |
          DevOps Tools Stack Deployed Successfully!
          
          Services Available:
          - SonarQube: http://{{ ansible_default_ipv4.address }}:9000 (admin/admin)
          - Nexus Repository: http://{{ ansible_default_ipv4.address }}:8081 (admin/admin123)
          - GitLab: http://{{ ansible_default_ipv4.address }}:80 (root/password)
          - Docker Registry: http://{{ ansible_default_ipv4.address }}:5000
          
          Management Scripts:
          - /usr/local/bin/devops-status.sh - Check all services status
          - /usr/local/bin/devops-backup.sh - Backup all tools data
          - /usr/local/bin/devops-restore.sh - Restore tools data
      tags: info
