---
# Multi-Cloud and Hybrid Cloud Deployment Playbook
# Usage: ansible-playbook -i inventory/ playbooks/multi-cloud-deployment.yml

- name: Setup Multi-Cloud Management
  hosts: cloud_management
  become: yes
  gather_facts: yes
  
  vars:
    cloud_tools_path: /opt/cloud-tools
    kubectl_version: "1.28.2"
    helm_version: "3.12.3"
    terraform_version: "1.6.2"
    
  pre_tasks:
    - name: Create cloud tools directory
      file:
        path: "{{ cloud_tools_path }}/{{ item }}"
        state: directory
        mode: '0755'
      with_items:
        - bin
        - configs
        - scripts
        - templates
        - credentials
      tags: directories

  tasks:
    - name: Install cloud CLI tools
      get_url:
        url: "{{ item.url }}"
        dest: "/tmp/{{ item.name }}"
        mode: '0644'
      with_items:
        - name: "awscli.zip"
          url: "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip"
        - name: "azure-cli.tar.gz"
          url: "https://aka.ms/InstallAzureCLIDeb"
        - name: "gcloud-cli.tar.gz"
          url: "https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/google-cloud-cli-linux-x86_64.tar.gz"
      tags: cloud_cli_download

    - name: Install AWS CLI
      unarchive:
        src: "/tmp/awscli.zip"
        dest: "/tmp"
        remote_src: yes
      tags: aws_cli

    - name: Install AWS CLI binary
      shell: /tmp/aws/install --install-dir {{ cloud_tools_path }} --bin-dir {{ cloud_tools_path }}/bin
      args:
        creates: "{{ cloud_tools_path }}/bin/aws"
      tags: aws_cli

    - name: Install Azure CLI
      shell: |
        curl -sL https://aka.ms/InstallAzureCLIDeb | bash
      args:
        creates: /usr/bin/az
      tags: azure_cli

    - name: Install Google Cloud CLI
      unarchive:
        src: "/tmp/gcloud-cli.tar.gz"
        dest: "{{ cloud_tools_path }}"
        remote_src: yes
      tags: gcp_cli

    - name: Install kubectl
      get_url:
        url: "https://dl.k8s.io/release/v{{ kubectl_version }}/bin/linux/amd64/kubectl"
        dest: "{{ cloud_tools_path }}/bin/kubectl"
        mode: '0755'
      tags: kubectl

    - name: Install Helm
      get_url:
        url: "https://get.helm.sh/helm-v{{ helm_version }}-linux-amd64.tar.gz"
        dest: "/tmp/helm.tar.gz"
        mode: '0644'
      tags: helm

    - name: Extract Helm
      unarchive:
        src: "/tmp/helm.tar.gz"
        dest: "/tmp"
        remote_src: yes
      tags: helm

    - name: Install Helm binary
      copy:
        src: "/tmp/linux-amd64/helm"
        dest: "{{ cloud_tools_path }}/bin/helm"
        mode: '0755'
        remote_src: yes
      tags: helm

    - name: Create cloud configuration templates
      template:
        src: "multi-cloud/{{ item }}.j2"
        dest: "{{ cloud_tools_path }}/configs/{{ item }}"
        mode: '0644'
      with_items:
        - aws-config
        - azure-config
        - gcp-config
        - kubeconfig-template
      tags: cloud_configs

    - name: Create multi-cloud management scripts
      template:
        src: "multi-cloud/{{ item }}.sh.j2"
        dest: "{{ cloud_tools_path }}/scripts/{{ item }}.sh"
        mode: '0755'
      with_items:
        - cloud-switch
        - multi-cloud-deploy
        - cloud-sync
        - cost-optimization
        - security-scan
      tags: management_scripts

- name: Deploy to AWS
  hosts: aws_targets
  become: yes
  gather_facts: yes
  
  vars:
    cloud_provider: aws
    
  tasks:
    - name: Configure AWS credentials
      template:
        src: multi-cloud/aws-credentials.j2
        dest: /root/.aws/credentials
        mode: '0600'
      tags: aws_creds

    - name: Deploy AWS-specific resources
      include_tasks: "cloud-specific/aws-deployment.yml"
      tags: aws_deploy

    - name: Setup AWS CloudWatch monitoring
      include_tasks: "cloud-specific/aws-monitoring.yml"
      tags: aws_monitoring

    - name: Configure AWS Load Balancer
      include_tasks: "cloud-specific/aws-loadbalancer.yml"
      tags: aws_lb

    - name: Setup AWS RDS
      include_tasks: "cloud-specific/aws-rds.yml"
      when: deploy_database | default(true)
      tags: aws_rds

- name: Deploy to Azure
  hosts: azure_targets
  become: yes
  gather_facts: yes
  
  vars:
    cloud_provider: azure
    
  tasks:
    - name: Configure Azure credentials
      shell: |
        az login --service-principal \
          --username {{ azure_client_id }} \
          --password {{ azure_client_secret }} \
          --tenant {{ azure_tenant_id }}
      tags: azure_auth

    - name: Deploy Azure-specific resources
      include_tasks: "cloud-specific/azure-deployment.yml"
      tags: azure_deploy

    - name: Setup Azure Monitor
      include_tasks: "cloud-specific/azure-monitoring.yml"
      tags: azure_monitoring

    - name: Configure Azure Application Gateway
      include_tasks: "cloud-specific/azure-appgateway.yml"
      tags: azure_appgw

    - name: Setup Azure SQL Database
      include_tasks: "cloud-specific/azure-sql.yml"
      when: deploy_database | default(true)
      tags: azure_sql

- name: Deploy to Google Cloud Platform
  hosts: gcp_targets
  become: yes
  gather_facts: yes
  
  vars:
    cloud_provider: gcp
    
  tasks:
    - name: Configure GCP credentials
      copy:
        content: "{{ gcp_service_account_key }}"
        dest: /root/.gcp/service-account.json
        mode: '0600'
      tags: gcp_creds

    - name: Authenticate with GCP
      shell: |
        gcloud auth activate-service-account --key-file=/root/.gcp/service-account.json
        gcloud config set project {{ gcp_project_id }}
      tags: gcp_auth

    - name: Deploy GCP-specific resources
      include_tasks: "cloud-specific/gcp-deployment.yml"
      tags: gcp_deploy

    - name: Setup GCP Monitoring
      include_tasks: "cloud-specific/gcp-monitoring.yml"
      tags: gcp_monitoring

    - name: Configure GCP Load Balancer
      include_tasks: "cloud-specific/gcp-loadbalancer.yml"
      tags: gcp_lb

    - name: Setup Cloud SQL
      include_tasks: "cloud-specific/gcp-cloudsql.yml"
      when: deploy_database | default(true)
      tags: gcp_sql

- name: Setup Hybrid Cloud Networking
  hosts: network_controllers
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Install VPN tools
      package:
        name:
          - strongswan
          - openvpn
          - wireguard
        state: present
      tags: vpn_tools

    - name: Configure site-to-site VPN
      template:
        src: "networking/{{ item }}.conf.j2"
        dest: "/etc/{{ item }}/{{ item }}.conf"
        mode: '0600'
      with_items:
        - strongswan
        - openvpn
        - wireguard
      notify: "restart {{ item }}"
      tags: vpn_config

    - name: Setup cloud interconnect
      include_tasks: "networking/{{ cloud_provider }}-interconnect.yml"
      vars:
        cloud_provider: "{{ item }}"
      with_items:
        - aws
        - azure
        - gcp
      when: "item in enabled_cloud_providers | default(['aws'])"
      tags: cloud_interconnect

    - name: Configure hybrid DNS
      template:
        src: networking/hybrid-dns.conf.j2
        dest: /etc/bind/hybrid-dns.conf
        mode: '0644'
      notify: restart bind9
      tags: hybrid_dns

    - name: Setup service mesh for multi-cloud
      include_tasks: "service-mesh/multi-cloud-mesh.yml"
      when: multi_cloud_service_mesh | default(false)
      tags: service_mesh

- name: Deploy Multi-Cloud Kubernetes
  hosts: kubernetes_masters
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Setup multi-cluster Kubernetes
      include_tasks: "kubernetes/multi-cluster-setup.yml"
      tags: multi_cluster

    - name: Install Admiralty for multi-cluster scheduling
      kubernetes.core.helm:
        name: admiralty
        chart_ref: admiralty/multicluster-scheduler
        release_namespace: admiralty-system
        create_namespace: true
      tags: admiralty

    - name: Configure cross-cluster service discovery
      template:
        src: kubernetes/cross-cluster-discovery.yml.j2
        dest: /tmp/cross-cluster-discovery.yml
        mode: '0644'
      tags: service_discovery

    - name: Apply cross-cluster configuration
      kubernetes.core.k8s:
        src: /tmp/cross-cluster-discovery.yml
        state: present
      tags: service_discovery

    - name: Setup multi-cloud ingress
      include_tasks: "kubernetes/multi-cloud-ingress.yml"
      tags: multi_cloud_ingress

- name: Setup Multi-Cloud Monitoring
  hosts: monitoring_servers
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Configure cloud-specific monitoring
      template:
        src: "monitoring/{{ item }}-monitoring.yml.j2"
        dest: "{{ observability_stack_path | default('/opt/observability') }}/prometheus/{{ item }}-monitoring.yml"
        mode: '0644'
      with_items:
        - aws-cloudwatch
        - azure-monitor
        - gcp-stackdriver
      tags: cloud_monitoring

    - name: Setup cross-cloud alerting
      template:
        src: monitoring/multi-cloud-alerts.yml.j2
        dest: "{{ observability_stack_path | default('/opt/observability') }}/prometheus/multi-cloud-alerts.yml"
        mode: '0644'
      tags: multi_cloud_alerts

    - name: Create multi-cloud dashboard
      template:
        src: monitoring/multi-cloud-dashboard.json.j2
        dest: "{{ observability_stack_path | default('/opt/observability') }}/grafana/dashboards/multi-cloud-dashboard.json"
        mode: '0644'
      tags: multi_cloud_dashboard

    - name: Setup cost monitoring across clouds
      template:
        src: monitoring/cost-monitoring.py.j2
        dest: /usr/local/bin/cost-monitoring.py
        mode: '0755'
      tags: cost_monitoring

    - name: Schedule cost monitoring
      cron:
        name: "Multi-cloud cost monitoring"
        minute: "0"
        hour: "8"
        job: "/usr/local/bin/cost-monitoring.py"
      tags: cost_monitoring_cron

- name: Setup Multi-Cloud Security
  hosts: security_controllers
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Configure cloud security scanning
      template:
        src: "security/{{ item }}-security-scan.sh.j2"
        dest: "/usr/local/bin/{{ item }}-security-scan.sh"
        mode: '0755'
      with_items:
        - aws
        - azure
        - gcp
      tags: security_scanning

    - name: Setup cross-cloud identity management
      include_tasks: "security/cross-cloud-identity.yml"
      tags: identity_management

    - name: Configure cloud security policies
      template:
        src: "security/{{ item }}-security-policy.json.j2"
        dest: "{{ cloud_tools_path }}/configs/{{ item }}-security-policy.json"
        mode: '0644'
      with_items:
        - aws
        - azure
        - gcp
      tags: security_policies

    - name: Setup compliance monitoring
      template:
        src: security/compliance-monitoring.py.j2
        dest: /usr/local/bin/compliance-monitoring.py
        mode: '0755'
      tags: compliance_monitoring

- name: Test Multi-Cloud Deployment
  hosts: test_controllers
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Run multi-cloud connectivity tests
      shell: "{{ cloud_tools_path }}/scripts/cloud-connectivity-test.sh"
      register: connectivity_test
      tags: connectivity_test

    - name: Test cross-cloud service communication
      shell: "{{ cloud_tools_path }}/scripts/service-communication-test.sh"
      register: service_test
      tags: service_test

    - name: Validate multi-cloud load balancing
      shell: "{{ cloud_tools_path }}/scripts/load-balancing-test.sh"
      register: lb_test
      tags: lb_test

    - name: Generate multi-cloud test report
      template:
        src: multi-cloud/test-report.html.j2
        dest: "{{ cloud_tools_path }}/reports/multi-cloud-test-{{ ansible_date_time.date }}.html"
        mode: '0644'
      tags: test_report

  handlers:
    - name: restart strongswan
      service:
        name: strongswan
        state: restarted

    - name: restart openvpn
      service:
        name: openvpn
        state: restarted

    - name: restart wireguard
      service:
        name: wg-quick@wg0
        state: restarted

    - name: restart bind9
      service:
        name: bind9
        state: restarted

- name: Display Multi-Cloud Summary
  hosts: cloud_management[0]
  gather_facts: no
  
  tasks:
    - name: Display multi-cloud deployment summary
      debug:
        msg: |
          ☁️ Multi-Cloud and Hybrid Cloud Deployment Complete!
          
          🌐 Cloud Providers Configured:
          {% for provider in enabled_cloud_providers | default(['aws', 'azure', 'gcp']) %}
          - {{ provider | upper }}: ✅ Configured and deployed
          {% endfor %}
          
          🔧 Management Tools Installed:
          - AWS CLI: {{ cloud_tools_path }}/bin/aws
          - Azure CLI: /usr/bin/az
          - Google Cloud CLI: {{ cloud_tools_path }}/google-cloud-sdk/bin/gcloud
          - kubectl: {{ cloud_tools_path }}/bin/kubectl
          - Helm: {{ cloud_tools_path }}/bin/helm
          
          🌉 Hybrid Networking:
          - Site-to-site VPN configured
          - Cloud interconnects established
          - Hybrid DNS resolution
          - Cross-cloud service mesh (if enabled)
          
          ☸️ Multi-Cloud Kubernetes:
          - Multi-cluster setup complete
          - Cross-cluster service discovery
          - Multi-cloud ingress configured
          - Workload scheduling across clouds
          
          📊 Monitoring & Security:
          - Cross-cloud monitoring dashboards
          - Multi-cloud alerting rules
          - Cost monitoring across providers
          - Security scanning and compliance
          
          📍 Key Locations:
          - Cloud tools: {{ cloud_tools_path }}/
          - Management scripts: {{ cloud_tools_path }}/scripts/
          - Configuration files: {{ cloud_tools_path }}/configs/
          - Test reports: {{ cloud_tools_path }}/reports/
          
          🚀 Next Steps:
          1. Configure DNS for multi-cloud load balancing
          2. Set up cross-cloud backup and disaster recovery
          3. Implement multi-cloud cost optimization
          4. Test failover scenarios
          5. Monitor cross-cloud performance
      tags: summary
