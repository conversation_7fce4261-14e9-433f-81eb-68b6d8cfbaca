---
# CI/CD Infrastructure Setup Playbook
# Usage: ansible-playbook -i inventory/ playbooks/cicd-setup.yml

- name: Setup CI/CD Infrastructure
  hosts: cicd_servers
  become: yes
  gather_facts: yes
  serial: 1
  
  vars:
    setup_jenkins: true
    setup_docker: true
    setup_sonarqube: false
    setup_nexus: false
    
  pre_tasks:
    - name: Update system packages
      package:
        update_cache: yes
        upgrade: dist
      tags: system_update

    - name: Install essential packages
      package:
        name:
          - curl
          - wget
          - git
          - unzip
          - software-properties-common
        state: present
      tags: prerequisites

  roles:
    - role: common
      tags: common
      
    - role: docker
      when: setup_docker
      tags: docker
      
    - role: jenkins
      when: setup_jenkins
      tags: jenkins
      
    - role: sonarqube
      when: setup_sonarqube
      tags: sonarqube
      
    - role: nexus
      when: setup_nexus
      tags: nexus

  post_tasks:
    - name: Configure firewall for CI/CD services
      ufw:
        rule: allow
        port: "{{ item }}"
        proto: tcp
      with_items:
        - "8080"  # Jenkins
        - "9000"  # SonarQube
        - "8081"  # Nexus
        - "3000"  # Grafana
        - "9090"  # Prometheus
      when: ansible_os_family == "Debian"
      tags: firewall

    - name: Create CI/CD service status check script
      template:
        src: cicd-status.sh.j2
        dest: /usr/local/bin/cicd-status.sh
        mode: '0755'
      tags: monitoring

    - name: Display CI/CD services information
      debug:
        msg: |
          CI/CD Infrastructure Setup Complete!
          
          Services Available:
          {% if setup_jenkins %}
          - Jenkins: http://{{ ansible_default_ipv4.address }}:8080
            Admin User: {{ jenkins_admin_username }}
          {% endif %}
          {% if setup_docker %}
          - Docker: Installed and configured
          {% endif %}
          {% if setup_sonarqube %}
          - SonarQube: http://{{ ansible_default_ipv4.address }}:9000
          {% endif %}
          {% if setup_nexus %}
          - Nexus: http://{{ ansible_default_ipv4.address }}:8081
          {% endif %}
      tags: info
