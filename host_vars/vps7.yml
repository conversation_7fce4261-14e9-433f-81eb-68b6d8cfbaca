---
# Host-specific variables for vps7

# Host identification
ansible_host: vps7.example.com
ansible_user: deploy
ansible_ssh_private_key_file: ~/.ssh/vps7_key

# Resource allocation
memory_total: "4G"
cpu_cores: 2
disk_space: "80G"

# Network configuration
public_ip: "************"
private_ip: "*********"
network_interface: eth0

# Application-specific settings
applications:
  - name: webapp
    port: 8080
    memory_limit: "1G"
    cpu_limit: "1"
  - name: api
    port: 8081
    memory_limit: "512M"
    cpu_limit: "0.5"

# Backup configuration
backup_destination: "s3://backups-bucket/vps7/"
backup_schedule: "0 3 * * *"  # Daily at 3 AM

# Monitoring
monitoring_agent_enabled: true
log_shipping_enabled: true
metrics_collection_interval: 60

# Security
fail2ban_jail_local:
  ssh:
    enabled: true
    port: "{{ ssh_port }}"
    maxretry: 3
    bantime: 3600

# Custom firewall rules
custom_firewall_rules:
  - port: 8080
    protocol: tcp
    source: "0.0.0.0/0"
  - port: 8081
    protocol: tcp
    source: "10.0.0.0/8"
