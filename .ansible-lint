# Ansible Lint configuration

# Exclude specific rules
skip_list:
  - yaml[line-length]  # Allow longer lines in some cases
  - name[casing]       # Allow flexible naming conventions

# Use default rules with some customizations
use_default_rules: true

# Exclude certain files/directories
exclude_paths:
  - .cache/
  - .github/
  - molecule/
  - .ansible/

# Enable offline mode (don't check for newer versions)
offline: false

# Set the verbosity level
verbosity: 1

# Enable colored output
colored: true

# Warn on unknown tags
warn_list:
  - experimental
