---
# Validation playbook to test infrastructure
# Usage: ansible-playbook -i inventory/ tests/test_playbooks.yml

- name: Validate infrastructure
  hosts: all
  gather_facts: yes
  become: no
  
  tasks:
    - name: Check if hosts are reachable
      ping:
      tags: connectivity

    - name: Gather system information
      setup:
      tags: facts

    - name: Check disk space
      shell: df -h /
      register: disk_space
      tags: disk

    - name: Validate disk space is sufficient
      assert:
        that:
          - disk_space.stdout | regex_search('([0-9]+)%') | regex_replace('%', '') | int < 90
        fail_msg: "Disk usage is above 90%"
      tags: disk

    - name: Check memory usage
      shell: free -m | awk 'NR==2{printf "%.2f", $3*100/$2}'
      register: memory_usage
      tags: memory

    - name: Validate memory usage
      assert:
        that:
          - memory_usage.stdout | float < 90
        fail_msg: "Memory usage is above 90%"
      tags: memory

    - name: Check if required services are running
      service_facts:
      tags: services

    - name: Validate SSH service is running
      assert:
        that:
          - ansible_facts.services['ssh.service'].state == 'running' or ansible_facts.services['sshd.service'].state == 'running'
        fail_msg: "SSH service is not running"
      tags: services

- name: Validate web servers
  hosts: webservers
  gather_facts: no
  become: no
  
  tasks:
    - name: Check if Nginx is running
      uri:
        url: "http://{{ ansible_default_ipv4.address }}"
        method: GET
        status_code: [200, 301, 302]
      tags: webserver

    - name: Check Nginx configuration
      uri:
        url: "http://{{ ansible_default_ipv4.address }}/nginx_status"
        method: GET
        status_code: 200
      ignore_errors: yes
      tags: webserver

- name: Validate database servers
  hosts: databases
  gather_facts: no
  become: no
  
  tasks:
    - name: Check MySQL service
      wait_for:
        port: 3306
        host: "{{ ansible_default_ipv4.address }}"
        timeout: 10
      tags: database

    - name: Test database connection
      mysql_db:
        name: mysql
        state: present
        login_host: localhost
        login_user: root
        login_password: "{{ vault_mysql_root_password }}"
      ignore_errors: yes
      tags: database
