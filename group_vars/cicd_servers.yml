---
# CI/CD servers group variables

# Jenkins configuration
jenkins_admin_username: admin
jenkins_admin_password: "{{ vault_jenkins_admin_password }}"
jenkins_port: 8080
jenkins_java_heap_size: "2g"

# Essential Jenkins plugins for CI/CD
jenkins_plugins:
  - ant
  - build-timeout
  - credentials-binding
  - email-ext
  - git
  - github-branch-source
  - gradle
  - pipeline-github-lib
  - pipeline-stage-view
  - ssh-slaves
  - timestamper
  - workflow-aggregator
  - ws-cleanup
  - blueocean
  - docker-workflow
  - kubernetes
  - ansible
  - sonar
  - nexus-artifact-uploader
  - slack
  - junit
  - jacoco
  - htmlpublisher

# Docker configuration
docker_users:
  - jen<PERSON>
  - "{{ ansible_user }}"

docker_daemon_options:
  storage-driver: overlay2
  log-driver: json-file
  log-opts:
    max-size: "10m"
    max-file: "3"
  live-restore: true
  insecure-registries:
    - "{{ docker_registry_url | default('registry.example.com:5000') }}"

# Docker Compose version
docker_compose_version: "2.20.2"

# Jenkins job templates
jenkins_job_templates:
  - name: "build-template"
    type: "pipeline"
    description: "Standard build pipeline template"
  - name: "deploy-template"
    type: "pipeline"
    description: "Standard deployment pipeline template"

# Build tools configuration
build_tools:
  maven:
    version: "3.9.4"
    home: "/opt/maven"
  gradle:
    version: "8.3"
    home: "/opt/gradle"
  nodejs:
    version: "18.17.0"
    home: "/opt/nodejs"

# Security settings
jenkins_security_realm: "jenkins"
jenkins_authorization_strategy: "loggedInUsersCanDoAnything"
jenkins_csrf_protection: true

# Backup configuration
jenkins_backup_enabled: true
jenkins_backup_schedule: "0 2 * * *"  # Daily at 2 AM
jenkins_backup_retention_days: 30

# Monitoring
jenkins_monitoring_enabled: true
jenkins_prometheus_plugin: true
