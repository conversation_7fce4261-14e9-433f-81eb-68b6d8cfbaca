---
# Web servers group variables

# Nginx configuration
nginx_user: www-data
nginx_client_max_body_size: 64m
nginx_server_tokens: off

# Virtual hosts
nginx_vhosts:
  - server_name: "{{ domain_name }}"
    document_root: "/var/www/{{ domain_name }}"
    index: "index.html index.php"
    ssl: "{{ ssl_enabled }}"
    access_log: "/var/log/nginx/{{ domain_name }}_access.log"
    error_log: "/var/log/nginx/{{ domain_name }}_error.log"

# PHP configuration (if needed)
php_enabled: false
php_version: "8.1"
php_memory_limit: "256M"
php_max_execution_time: 300
php_upload_max_filesize: "64M"

# Security
nginx_security_headers:
  - "X-Frame-Options DENY"
  - "X-Content-Type-Options nosniff"
  - "X-XSS-Protection 1; mode=block"
  - "Strict-Transport-Security max-age=31536000; includeSubDomains"

# Rate limiting
nginx_rate_limit_enabled: true
nginx_rate_limit: "10r/s"

# Caching
nginx_cache_enabled: true
nginx_cache_path: "/var/cache/nginx"
nginx_cache_max_size: "1g"

# Log rotation
nginx_log_rotation:
  frequency: daily
  retention: 30
  compress: true
