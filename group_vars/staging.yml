---
# Staging environment variables

environment: staging
domain_name: "staging.example.com"

# Relaxed security for staging
ssh_port: 22
firewall_strict_mode: false
fail2ban_enabled: false

# Performance settings (lower than production)
nginx_worker_processes: 1
nginx_worker_connections: 1024
nginx_keepalive_timeout: 65

# Database settings
mysql_max_connections: 50
mysql_innodb_buffer_pool_size: "256M"

# Monitoring
monitoring_retention_days: 30
alerting_enabled: false

# SSL settings
ssl_enabled: false
ssl_redirect_http: false

# Backup settings
backup_enabled: true
backup_encryption_enabled: false
backup_offsite_enabled: false

# Log settings
log_level: info
debug_mode: true

# Resource limits
memory_limit: "1G"
cpu_limit: "1"

# Development features
hot_reload_enabled: true
debug_toolbar_enabled: true
