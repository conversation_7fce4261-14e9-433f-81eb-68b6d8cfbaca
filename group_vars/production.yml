---
# Production environment variables

environment: production
domain_name: "prod.example.com"

# Security settings for production
ssh_port: 2222
firewall_strict_mode: true
fail2ban_enabled: true

# Performance settings
nginx_worker_processes: auto
nginx_worker_connections: 2048
nginx_keepalive_timeout: 30

# Database settings
mysql_max_connections: 200
mysql_innodb_buffer_pool_size: "1G"

# Monitoring
monitoring_retention_days: 90
alerting_enabled: true

# SSL settings
ssl_enabled: true
ssl_redirect_http: true
ssl_hsts_enabled: true

# Backup settings
backup_enabled: true
backup_encryption_enabled: true
backup_offsite_enabled: true

# Log settings
log_level: warn
debug_mode: false

# Resource limits
memory_limit: "2G"
cpu_limit: "2"

# Maintenance window
maintenance_window: "02:00-04:00"
maintenance_day: "Sunday"
