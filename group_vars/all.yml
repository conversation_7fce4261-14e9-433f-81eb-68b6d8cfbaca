---
# Global variables for all hosts

# Environment configuration
environment: "{{ env | default('production') }}"
domain_name: "example.com"

# Common user configuration
deploy_user: deploy
deploy_group: deploy
deploy_home: /home/<USER>

# Application configuration
app_base_path: /opt/apps
log_base_path: /var/log/apps
backup_base_path: /opt/backups

# Security configuration
ssh_port: 22
firewall_enabled: true

# Monitoring configuration
monitoring_enabled: true
log_aggregation_enabled: true

# Notification configuration
notification_email: <EMAIL>
slack_webhook_url: "{{ vault_slack_webhook_url | default('') }}"

# Backup configuration
backup_enabled: true
backup_retention_days: 30
backup_schedule: "0 2 * * *"  # Daily at 2 AM

# SSL/TLS configuration
ssl_enabled: true
ssl_certificate_path: /etc/ssl/certs
ssl_private_key_path: /etc/ssl/private

# Database configuration
db_backup_enabled: true
db_backup_schedule: "0 1 * * *"  # Daily at 1 AM

# Performance tuning
max_open_files: 65536
tcp_keepalive_time: 7200

# Time and locale
timezone: "UTC"
locale: "en_US.UTF-8"

# Package management
package_update_cache: true
package_upgrade: false  # Set to true for automatic upgrades

# Log rotation
logrotate_enabled: true
logrotate_frequency: daily
logrotate_retention: 30
