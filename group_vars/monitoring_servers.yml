---
# Monitoring servers group variables

# Prometheus configuration
prometheus_version: "2.45.0"
prometheus_port: 9090
prometheus_retention: "30d"
prometheus_storage_path: "/opt/prometheus/data"

# Prometheus targets
prometheus_targets:
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]
  - job_name: "node-exporter"
    static_configs:
      - targets: ["{{ groups['all'] | map('extract', hostvars, 'ansible_default_ipv4') | map(attribute='address') | map('regex_replace', '^(.*)$', '\\1:9100') | list }}"]
  - job_name: "jenkins"
    static_configs:
      - targets: ["{{ groups['cicd_servers'] | map('extract', hostvars, 'ansible_default_ipv4') | map(attribute='address') | map('regex_replace', '^(.*)$', '\\1:8080') | list }}"]

# Grafana configuration
grafana_version: "10.0.3"
grafana_port: 3000
grafana_admin_user: admin
grafana_admin_password: "{{ vault_grafana_admin_password }}"

# Grafana datasources
grafana_datasources:
  - name: "Prometheus"
    type: "prometheus"
    url: "http://prometheus:9090"
    access: "proxy"
    is_default: true

# Grafana dashboards
grafana_dashboards:
  - dashboard_id: 1860
    revision: 27
    datasource: "Prometheus"
  - dashboard_id: 3662
    revision: 2
    datasource: "Prometheus"

# Elasticsearch configuration
elasticsearch_version: "8.8.0"
elasticsearch_port: 9200
elasticsearch_heap_size: "1g"
elasticsearch_cluster_name: "devops-logs"

# Kibana configuration
kibana_version: "8.8.0"
kibana_port: 5601

# Logstash configuration
logstash_version: "8.8.0"
logstash_port: 5044
logstash_heap_size: "512m"

# Alertmanager configuration
alertmanager_version: "0.25.0"
alertmanager_port: 9093

# Alert rules
prometheus_alert_rules:
  - name: "system.rules"
    rules:
      - alert: "HighCPUUsage"
        expr: "100 - (avg by(instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[2m])) * 100) > 80"
        for: "2m"
        labels:
          severity: "warning"
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 2 minutes"
      
      - alert: "HighMemoryUsage"
        expr: "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85"
        for: "5m"
        labels:
          severity: "warning"
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes"

# Notification channels
alertmanager_slack_webhook: "{{ vault_slack_webhook_url }}"
alertmanager_email_to: "<EMAIL>"
alertmanager_email_from: "<EMAIL>"

# Data retention
monitoring_data_retention:
  prometheus: "30d"
  elasticsearch: "30d"
  grafana: "90d"

# Security
monitoring_auth_enabled: true
monitoring_ssl_enabled: false
