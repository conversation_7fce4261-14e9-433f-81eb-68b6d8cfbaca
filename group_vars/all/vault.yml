# This file should be encrypted with ansible-vault
# Run: ansible-vault encrypt group_vars/all/vault.yml
---
# Encrypted sensitive variables

# Database passwords
vault_mysql_root_password: "super_secure_root_password_123!"
vault_mysql_app_password: "secure_app_password_456!"

# API keys and tokens
vault_slack_webhook_url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
vault_github_token: "ghp_your_github_token_here"
vault_aws_access_key: "AKIAIOSFODNN7EXAMPLE"
vault_aws_secret_key: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"

# SSL certificates (base64 encoded)
vault_ssl_certificate: |
  LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t
  # Your SSL certificate content here
  LS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQ==

vault_ssl_private_key: |
  LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t
  # Your SSL private key content here
  LS0tLS1FTkQgUFJJVkFURSBLRVktLS0tLQ==

# Application secrets
vault_app_secret_key: "your-super-secret-app-key-here"
vault_jwt_secret: "jwt-signing-secret-key"

# Third-party service credentials
vault_smtp_password: "smtp_password_here"
vault_redis_password: "redis_password_here"

# Backup encryption keys
vault_backup_encryption_key: "backup-encryption-key-here"

# Monitoring credentials
vault_grafana_admin_password: "grafana_admin_password"
vault_prometheus_basic_auth: "prometheus:password"
